import {Dropdown, Menu, Typography, Avatar, Modal, message} from 'antd';
import {DownOutlined} from '@ant-design/icons';
import {useNavigate} from 'react-router-dom';
import React, {useState} from 'react';
import { clearAuthTokens } from '@/data/refresh_token_net';

/** 用户下拉组件：显示邮箱 + 下拉菜单（修改密码/退出登录） */
const UserDropdown: React.FC = () => {
    const navigate = useNavigate(); // 路由跳转工具（需在 Router 上下文内使用）
    const userEmail = '<EMAIL>'; // 实际应从全局状态/接口获取（如 Redux/Context/useState）

    // 🔹 修改密码：触发回调（如打开模态框、调用接口等）
    const handleChangePwd = () => {
        console.log('点击了「修改密码」');
        message.info('修改密码功能待实现');
        // 这里写实际逻辑：如 showModal(ChangePwdModal)
    };

    // 🔹 退出登录：触发回调（如清除 Token、跳转到登录页等）
    const handleLogout = () => {
        Modal.confirm({
            title: '确认退出',
            content: '您确定要退出登录吗？',
            okText: '确定',
            cancelText: '取消',
            onOk: () => {
                console.log('点击了「退出登录」');
                // 清除认证令牌
                clearAuthTokens();
                message.success('已退出登录');
                // 跳转到登录页面或首页
                navigate('/login');
            }
        });
    };

    // 下拉菜单的内容（antd Menu 组件）
    const downMenu = (
        <Menu>
            <Menu.Item key="change-pwd" onClick={handleChangePwd}>
                修改密码
            </Menu.Item>
            <Menu.Item key="logout" onClick={handleLogout}>
                退出登录
            </Menu.Item>
        </Menu>
    );

    return (
        <Dropdown
            overlay={downMenu}
            placement="bottomCenter" // 下拉方向（可选：topLeft, bottomRight 等）
            trigger={['hover', 'click']} // 触发方式（悬浮 + 点击）
        >
            {/* 下拉触发元素：头像 + 邮箱（可自定义样式） */}
            <div style={{cursor: 'pointer', display: 'flex', alignItems: 'center'}}>
                <Typography.Link>{userEmail}</Typography.Link>
            </div>
        </Dropdown>
    );
};

export default UserDropdown;