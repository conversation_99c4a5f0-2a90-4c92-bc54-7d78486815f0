import { Dropdown, Menu, Typography, Avatar } from 'antd';
import { useNavigate } from 'react-router-dom';
import React from 'react';

/** 用户下拉组件：显示邮箱 + 下拉菜单（账户管理/修改密码/退出登录） */
const UserDropdown: React.FC = () => {
    const navigate = useNavigate(); // 路由跳转工具（需在 Router 上下文内使用）
    const userEmail = '<EMAIL>'; // 实际应从全局状态/接口获取（如 Redux/Context/useState）

    // 🔹 账户管理：跳转到指定路由
    const handleAccountManage = () => {
        navigate('/account-manage'); // 替换为实际账户管理页面的路由
    };

    // 🔹 修改密码：触发回调（如打开模态框、调用接口等）
    const handleChangePwd = () => {
        console.log('点击了「修改密码」');
        // 这里写实际逻辑：如 showModal(ChangePwdModal)
    };

    // 🔹 退出登录：触发回调（如清除 Token、跳转到登录页等）
    const handleLogout = () => {
        console.log('点击了「退出登录」');
        // 这里写实际逻辑：如 removeToken(); navigate('/login')
    };

    // 下拉菜单的内容（antd Menu 组件）
    const menu = (
        <Menu>
            <Menu.Item key="account" onClick={handleAccountManage}>
        账户管理
        </Menu.Item>
        <Menu.Item key="change-pwd" onClick={handleChangePwd}>
        修改密码
        </Menu.Item>
        <Menu.Item key="logout" onClick={handleLogout}>
        退出登录
        </Menu.Item>
        </Menu>
);

    return (
        <Dropdown
            overlay={menu}         // 下拉菜单内容
    placement="bottomCenter" // 下拉方向（可选：topLeft, bottomRight 等）
    trigger={['hover', 'click']} // 触发方式（悬浮 + 点击）
>
    {/* 下拉触发元素：头像 + 邮箱（可自定义样式） */}
    <div style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}>
    <Avatar style={{ marginRight: 8 }}>
    {userEmail?.charAt(0).toUpperCase()} {/* 取邮箱首字母作为头像文字 */}
    </Avatar>
    <Typography.Link>{userEmail}</Typography.Link>
    </div>
    </Dropdown>
);
};

export default UserDropdown;