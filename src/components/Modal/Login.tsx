import React, { useState } from 'react';
import { Modal, Form, Input, Checkbox, Button, message } from 'antd';
import type { FormInstance } from 'antd/es/form';

// 表单数据类型
interface LoginFormValues {
    username: string;
    password: string;
    remember: boolean;
}

const LoginModal: React.FC = () => {
    const [visible, setVisible] = useState(true);
    const [loading, setLoading] = useState(false);
    const formRef = React.useRef<FormInstance>(null);

    // 处理登录提交
    const handleLogin = async (values: LoginFormValues) => {
        setLoading(true);
        try {
            setVisible(false);
        } catch (error) {
            message.error('登录失败，请检查用户名和密码');
        } finally {
            setLoading(false);
        }
    };

    // 取消登录
    const handleCancel = () => {
        formRef.current?.resetFields();
        setVisible(false);
    };

    return (
        <Modal
            title="用户登录"
            open={visible}
            onCancel={handleCancel}
            footer={null}
            centered
            maskClosable={false}
        >
            <Form
                ref={formRef}
                name="loginForm"
                initialValues={{ remember: true }}
                onFinish={handleLogin}
                layout="vertical"
                autoComplete="off"
            >
                {/* 用户名 */}
                <Form.Item
                    label="用户名"
                    name="username"
                    rules={[
                        { required: true, message: '请输入用户名' },
                        { min: 3, message: '用户名至少3个字符' }
                    ]}
                >
                    <Input placeholder="请输入用户名" />
                </Form.Item>

                {/* 密码 */}
                <Form.Item
                    label="密码"
                    name="password"
                    rules={[
                        { required: true, message: '请输入密码' },
                        { min: 6, message: '密码至少6个字符' }
                    ]}
                >
                    <Input.Password placeholder="请输入密码" />
                </Form.Item>

                {/* 记住登录状态 */}
                <Form.Item name="remember" valuePropName="checked">
                    <Checkbox>保持三十天登录状态</Checkbox>
                </Form.Item>

                {/* 登录按钮 */}
                <Form.Item>
                    <Button
                        type="primary"
                        htmlType="submit"
                        block
                        loading={loading}
                    >
                        登录
                    </Button>
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default LoginModal;