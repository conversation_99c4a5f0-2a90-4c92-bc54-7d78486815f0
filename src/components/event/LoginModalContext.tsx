// contexts/LoginModalContext.tsx
import React, { createContext, useState, useCallback, useContext } from 'react';
import { eventBus } from '@/components/event/login';
import LoginModal from "@/components/Modal/Login";

interface LoginModalContextType {
    openLoginModal: (options?: { message?: string }) => void;
    closeLoginModal: () => void;
}

const LoginModalContext = createContext<LoginModalContextType | null>(null);

export const LoginModalProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [visible, setVisible] = useState(false);

    const openLoginModal = useCallback((options = {}) => {
        setVisible(true);
    }, []);

    const closeLoginModal = useCallback(() => {
        setVisible(false);
    }, []);

    // 订阅全局事件
    React.useEffect(() => {
        eventBus.on('SHOW_LOGIN_MODAL', openLoginModal);
        return () => {
            eventBus.off('SHOW_LOGIN_MODAL', openLoginModal);
        };
    }, [openLoginModal]);

    const handleLogin = () => {
        // 实际登录逻辑
        console.log('执行登录操作');
        closeLoginModal();
    };

    return (
        <LoginModalContext.Provider value={{ openLoginModal, closeLoginModal }}>
            {children}
            {visible && (
                <LoginModal/>
            )}
        </LoginModalContext.Provider>
    );
};

export const useLoginModal = () => {
    const context = useContext(LoginModalContext);
    if (!context) {
        throw new Error('useLoginModal must be used within a LoginModalProvider');
    }
    return context;
};