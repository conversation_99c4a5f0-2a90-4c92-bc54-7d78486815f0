// utils/eventBus.ts
type EventHandler = (data?: any) => void;

class EventBus {
    private events: Record<string, EventHandler[]> = {};

    on(eventName: string, handler: EventHandler) {
        if (!this.events[eventName]) {
            this.events[eventName] = [];
        }
        this.events[eventName].push(handler);
    }

    off(eventName: string, handler: EventHandler) {
        const handlers = this.events[eventName];
        if (handlers) {
            this.events[eventName] = handlers.filter(h => h !== handler);
        }
    }

    emit(eventName: string, data?: any) {
        const handlers = this.events[eventName];
        if (handlers) {
            handlers.forEach(handler => handler(data));
        }
    }
}

// 创建全局单例事件总线
export const eventBus = new EventBus();