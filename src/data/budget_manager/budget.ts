import {AdConfigList, AdResponse, AdResult, API_BASE, request2Result} from "@/data/net";
import {apiClient} from "@/data/refresh_token_net";

export type AdBudget = {
    // ID
    id: number;
    // 预算标识
    budget_identifier: number;
    // 预算源名称
    name: string;
    // 预算源公司名称
    company_name: string;
    // 预算源联系人
    contact: string;
    // 预算源联系电话
    phone: string;
    // 是否启用
    enable: boolean;
};

export type AdBudgetIdentifier = {
    // 预算标识
    budget_identifier: number;
    // 预算源名称
    name: string;
}

// 定义表单数据类型
export type AdBudgetModify = {
    // 预算标识
    budget_identifier: number;
    // 预算源名称
    name: string;
    // 预算源公司名称
    company_name: string;
    // 预算源联系人
    contact: string;
    // 预算源联系电话
    phone: string;
    // 是否启用
    enable: boolean;
}


export function queryBudgetIdentifier(): Promise<AdResult<AdBudgetIdentifier[]>> {
    const value = apiClient.get(`${API_BASE}/budget/identifier`, {method: 'GET', mode: 'cors'})
        .then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<AdBudgetIdentifier[]>(value)
}

export function queryBudgetList(page: number, pageSize: number): Promise<AdResult<AdConfigList<AdBudget>>> {
    console.log('queryBudgetList')
    const value = apiClient.get(`${API_BASE}/budget/get?page=${page}&page_size=${pageSize}`, {
        method: 'GET',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<AdConfigList<AdBudget>>(value)
}


export function update(budget: any): Promise<AdResult<any>> {
    const value = apiClient.post(`${API_BASE}/budget/update`, budget, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<any>(value)
}

export function add(budget: any): Promise<AdResult<any>> {
    const value = apiClient.post(`${API_BASE}/budget/add`, budget, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<any>(value)
}

export function toggle(id: number, enable: boolean): Promise<AdResult<any>> {
    const value = apiClient.get(`${API_BASE}/budget/toggle?id=${id}&enable=${enable}`, {
        method: 'GET',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<any>(value)
}

export function remove(id: number): Promise<AdResult<any>> {
    const value = apiClient.delete(`${API_BASE}/budget/delete?id=${id}`, {
        method: 'DELETE',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<any>(value)
}

