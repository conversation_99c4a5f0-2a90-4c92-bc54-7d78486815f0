import {AdConfigAndSelectList, AdConfigList, AdResponse, AdResult, API_BASE, request2Result} from "@/data/net";
import {apiClient} from "@/data/refresh_token_net";
import {AdBudgetIdentifier} from "@/data/budget_manager/budget";

export type AdSource = {
    // 广告源ID
    id: number;

    // 预算源名称
    name: string;

    // 预算方ID
    budget_identifier: number;

    // 系统类型
    os_type: number;

    // 广告类型
    ad_type: number;

    // 广告源ID
    budget_ad_id: string;

    // 返点比例
    rebate_ratio?: number;

    // 是否开启定向设置
    open_directional_setting: boolean;

    // 允许展示区域
    area_ids?: string;

    // 允许展示品牌
    brand_ids?: string;

    // 允许安装应用
    installed_app_ids?: string;

    // 不允许展示应用包名
    no_allow_package_names?: string;

    // 是否开启频控设置
    open_frequency_setting: boolean;

    // 广告源请求上限
    source_request_limit?: number;

    // 广告源展示上限
    source_show_limit?: number;

    // 单设备请求上限
    request_limit?: number;

    // 单设备展示上限
    show_limit?: number;

    //虚拟设备请求上限
    virtual_request_limit?: number;

    // 单设备请求间隔
    request_interval?: number;

    // 报备应用ID
    app_id?: string;

    //报备应用Key
    app_key?: string;

    // 报备应用名称
    app_name?: string;

    // 报备应用包名
    app_package_name?: string;

    // 报备起应用版本
    app_version_min?: string;

    // 报备止应用版本
    app_version_max?: string;

    // 广告启用状态
    enable: boolean;
};
export type AdDirectionalSetting = {
    // 允许展示区域
    area_ids?: string;

    // 允许展示品牌
    brand_ids?: string;

    // 允许安装应用
    installed_app_ids?: string;

    // 不允许展示应用包名
    no_allow_package_names?: string;
};
export type AdReportSetting = {
    // 报备应用ID
    app_id?: string;

    //报备应用Key
    app_key?: string;

    // 报备应用名称
    app_name?: string;

    // 报备应用包名
    app_package_name?: string;

    // 报备起应用版本
    app_version_min?: string;

    // 报备止应用版本
    app_version_max?: string;
};
export type AdBudgetIdentifierSelect = {
    label: string;
    value: number;
}

export function querySourceList(page: number, pageSize: number, budget_identifier?: string, os_type?: string)
    : Promise<AdResult<AdConfigAndSelectList<AdSource, AdBudgetIdentifier>>> {
    let url = `${API_BASE}/source/get?page=${page}&page_size=${pageSize}`
    if (budget_identifier) {
        url += `&budget_identifier=${budget_identifier}`
    }
    if (os_type) {
        url += `&os_type=${os_type}`
    }
    const value = apiClient.get(url, {method: 'GET', mode: 'cors'})
        .then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<AdConfigAndSelectList<AdSource, AdBudgetIdentifier>>(value)
}

export function addSource(source: any): Promise<AdResult<any>> {
    const value = apiClient.post(`${API_BASE}/source/add`, source, {
        method: 'POST',
        mode: 'cors'
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<any>(value)
}

export function updateSource(source: any): Promise<AdResult<any>> {
    const value = apiClient.post(`${API_BASE}/source/update`, source, {
        method: 'POST',
        mode: 'cors'
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<any>(value)
}

export function removeSource(id: number): Promise<AdResult<any>> {
    const value = apiClient.delete(`${API_BASE}/source/delete?id=${id}`, {
        method: 'DELETE',
        mode: 'cors'
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<any>(value)
}

export function toggleSource(id: number, enable: boolean): Promise<AdResult<any>> {
    const value = apiClient.get(`${API_BASE}/source/toggle?id=${id}&enable=${enable}`, {
        method: 'GET',
        mode: 'cors'
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<any>(value)
}
