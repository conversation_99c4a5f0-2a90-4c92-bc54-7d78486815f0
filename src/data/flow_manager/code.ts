
import {AdResponse, AdResult, API_BASE, request2Result} from "@/data/net";
import {apiClient} from "@/data/refresh_token_net";
import {AdSlotSelection} from "@/data/flow_manager/slot";
import {AdMediaSelection} from "@/data/flow_manager/media";
import {AdFlowSelection} from "@/data/flow_manager/flow";

export type AdCodeResp = {
    id: number; // 广告位代码ID
    ad_slot_id: number; // 广告位ID
    ad_slot_name: string; // 广告位名称
    ad_source_id: number; // 广告源ID
    ad_source_name: string; // 广告源名称
    commission_rate: number | null; // 佣金比例
    reserve_price: number | null; // 底价
    is_enable: boolean; // 是否启用
};

export type AdCodeConfigList = {
    list: AdCodeResp[];
    count: number;
    slot_selection_list : AdSlotSelection[];
    media_selection_list : AdMediaSelection[];
    flow_selection_list : AdFlowSelection[];
    flow_selection:AdFlowSelection
    media_selection:AdMediaSelection
    slot_selection:AdSlotSelection
};


export type AdCodeCreateBody = {
    ad_slot_id: number; // 广告位ID
    ad_source_id: number; // 广告源ID
    commission_rate: number | null; // 佣金比例
    reserve_price: number | null; // 底价
};

export type AdCodeUpdateBody = {
    id: number; // 广告位代码ID
    ad_source_id: number; // 广告源ID
    commission_rate: number | null; // 佣金比例
    reserve_price: number | null; // 底价
};


export function queryCodeList(page: number, pageSize: number, slot_id?: number, enable?: boolean)
    : Promise<AdResult<AdCodeConfigList>> {
    let url = `${API_BASE}/code/get?page=${page}&page_size=${pageSize}`
    if (slot_id) {
        url += `&slot_id=${slot_id}`
    }
    if (enable !== undefined) {
        url += `&enable=${enable}`
    }
    const value = apiClient.get(url, {
        method: 'GET',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<AdCodeConfigList>(value)
}

export function addCode(code: any): Promise<AdResult<any>> {
    const value = apiClient.post(`${API_BASE}/code/add`, code, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<any>(value)
}

export function updateCode(code: any): Promise<AdResult<any>> {
    const value = apiClient.post(`${API_BASE}/code/update`, code, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<any>(value)
}

export function toggleCode(slot_id: number, code_id: number, enable: boolean): Promise<AdResult<any>> {
    const value = apiClient.get(`${API_BASE}/code/toggle?slot_id=${slot_id}&code_id=${code_id}&enable=${enable}`, {
        method: 'GET',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<any>(value)
}

export function removeCode(slot_id: number, code_id: number): Promise<AdResult<any>> {
    const value = apiClient.delete(`${API_BASE}/code/delete?slot_id=${slot_id}&code_id=${code_id}`, {
        method: 'DELETE',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<any>(value)
}
