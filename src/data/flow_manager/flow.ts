import {AdConfigList, AdResponse, AdResult, API_BASE, request2Result} from "@/data/net";
import {apiClient} from "@/data/refresh_token_net";
export type AdFlow = {
    // ID
    id: number;
    // 预算源名称
    name: string;
    // 预算源公司名称
    company_name: string;
    // 预算源联系人
    contact: string;
    // 预算源联系电话
    phone: string;
    // 是否启用
    enable: boolean;
};

export type AdFlowSelection = {
    id: number,
    name: string,
    enable: boolean,
}

// 定义表单数据类型
export type AdFlowModify = {
    // 预算源名称
    name: string;
    // 预算源公司名称
    company_name: string;
    // 预算源联系人
    contact: string;
    // 预算源联系电话
    phone: string;
    // 是否启用
    enable: boolean;
}

export function queryFlowList(page: number, pageSize: number): Promise<AdResult<AdConfigList<AdFlow>>> {
    const value = apiClient.get(`${API_BASE}/flow/get?page=${page}&page_size=${pageSize}`, {
        method: 'GET',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<AdConfigList<AdFlow>>(value)
}

export function toggleFlow(id: number, enable: boolean): Promise<AdResult<any>> {
    const value = apiClient.get(`${API_BASE}/flow/toggle?id=${id}&enable=${enable}`, {
        method: 'GET',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<any>(value)
}

export function removeFlow(id: number): Promise<AdResult<any>> {
    const value = apiClient.delete(`${API_BASE}/flow/delete?id=${id}`, {
        method: 'DELETE',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<any>(value)
}

export function addFlow(flow: any): Promise<AdResult<any>> {
    const value = apiClient.post(`${API_BASE}/flow/add`, flow, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<any>(value)
}

export function updateFlow(flow: any): Promise<AdResult<any>> {
    const value = apiClient.post(`${API_BASE}/flow/update`, flow, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<any>(value)
}
