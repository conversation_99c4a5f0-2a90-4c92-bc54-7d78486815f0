import {AdResponse, AdResult, API_BASE, request2Result} from "../net";
import { apiClient } from "../refresh_token_net";
import {AdFlowSelection} from "@/data/flow_manager/flow";
/**
 * AdMediaModel 媒体
 */
export type AdMediaModel = {
    /**
     * ID 是主键，自动递增
     */
    id: number;

    /**
     * 名称
     */
    name: string;

    /**
     * 流量平台ID
     */
    flow_id: number;

    /**
     * 系统类型 (OS 类型)
     */
    os_type: number;

    /**
     * 媒体包名
     */
    package_name: string;

    /**
     * 应用包下载地址 (可选)
     */
    package_download_url?: string;

    /**
     * 媒体图标地址 (可选)
     */
    package_icon_url?: string;

    /**
     * 是否启用
     */
    enable: boolean;
};


export type AdMediaConfigList = {
    list: AdMediaModel[];
    count: number;
    flow_selection_list : AdFlowSelection[];
    history_flow:AdFlowSelection
};

export type AdMediaSelection = {
    id: number,
    name: string,
    enable: boolean,
}

export type AdMediaCreateBody = {
    name: string | null; // 媒体名称
    flow_id: number | null; // 流量平台ID
    os_type: number | null; // 系统类型
    package_name: string | null; // 媒体包名
    package_download_url: string | null; // 应用包下载地址
    package_icon_url: string | null; // 媒体图标
};

export type AdMediaUpdateBody = {
    id: number | null; // 媒体ID
    name: string | null; // 媒体名称
    os_type: number | null; // 系统类型
    package_name: string | null; // 媒体包名
    package_download_url: string | null; // 应用包下载地址
    package_icon_url: string | null; // 媒体图标
};

export function getMediaSelection4Flow(flow_id: number): Promise<AdResult<AdMediaSelection[]>> {
    const value = apiClient.get(`${API_BASE}/media/selection?flow_id=${flow_id}`, {
        method: 'GET',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<AdMediaSelection[]>(value)
}

export function queryMediaList(page: number, pageSize: number, flow_id?: number, os_type?: number, enable?: boolean)
    : Promise<AdResult<AdMediaConfigList>> {
    let url = `${API_BASE}/media/get?page=${page}&page_size=${pageSize}`
    if (flow_id) {
        url += `&flow_id=${flow_id}`
    }
    if (os_type) {
        url += `&os_type=${os_type}`
    }
    if (enable !== undefined) {
        url += `&enable=${enable}`
    }
    const value = apiClient.get(url, {
        method: 'GET',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<AdMediaConfigList>(value)
}

export function toggleMedia(id: number, enable: boolean): Promise<AdResult<any>> {
    const value = apiClient.get(`${API_BASE}/media/toggle?id=${id}&enable=${enable}`, {
        method: 'GET',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<any>(value)
}

export function removeMedia(id: number): Promise<AdResult<any>> {
    const value = apiClient.delete(`${API_BASE}/media/delete?id=${id}`, {
        method: 'DELETE',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<any>(value)
}

export function addMedia(media: any): Promise<AdResult<any>> {
    const value = apiClient.post(`${API_BASE}/media/add`, media, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<any>(value)
}

export function updateMedia(media: any): Promise<AdResult<any>> {
    const value = apiClient.post(`${API_BASE}/media/update`, media, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<any>(value)
}
