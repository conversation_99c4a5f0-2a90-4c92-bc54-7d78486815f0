import {AdFrequencySetting} from "@/data/common";
import {AdMediaSelection} from "@/data/flow_manager/media";
import {AdResponse, AdResult, API_BASE, request2Result} from "@/data/net";
import {apiClient} from "@/data/refresh_token_net";
import {AdFlowSelection} from "@/data/flow_manager/flow";

export type AdSlotModel = {
    id: number; // 广告位ID
    name: string; // 广告位名称
    slot_id: number; // 广告位唯一标识
    media_id: number; // 媒体ID
    ad_type: number; // 广告类型
    request_timeout: number; // 请求超时时间
    open_frequency_setting: boolean; // 是否开启频控设置
    source_request_limit: number | undefined; // 广告源请求上限
    source_show_limit: number |undefined; // 广告源展示上限
    request_limit: number | undefined; // 单设备请求上限
    show_limit: number | undefined; // 单设备展示上限
    virtual_request_limit: number | undefined; // 虚拟设备请求上限
    request_interval: number | undefined; // 单设备请求间隔
    enable: boolean; // 是否启用
};


export type AdSlotConfigList = {
    list: AdSlotModel[];
    count: number;
    media_selection_list : AdMediaSelection[];
    flow_selection_list : AdFlowSelection[];
    flow_selection : AdFlowSelection;
    media_selection : AdMediaSelection;
};


export type AdSlotCreateBody = {
    name: string | undefined; // 广告位名称
    media_id: number | undefined; // 媒体ID
    ad_type: number | undefined; // 广告类型
    request_timeout: number | undefined; // 请求超时时间
    open_frequency_setting: boolean; // 是否开启频控设置
    ad_frequency_setting: AdFrequencySetting | undefined; // 频控设置对象
};


export type AdSlotUpdateBody = {
    id: number | undefined; // 广告位ID
    name: string | undefined; // 广告位名称
    ad_type: number | undefined; // 广告类型
    request_timeout: number | undefined; // 请求超时时间
    open_frequency_setting: boolean; // 是否开启频控设置
    ad_frequency_setting: AdFrequencySetting | undefined; // 频控设置对象
};


export type AdSlotModify = {
    id: number; // 广告位ID
    name: string; // 广告位名
    media_id: number; // 广告位唯一标识
    ad_type: number; // 广告类型
    request_timeout: number| undefined; // 请求超时时间
    open_frequency_setting: boolean; // 是否开启频控设置
    source_request_limit: number | undefined; // 广告源请求上限
    source_show_limit: number | undefined; // 广告源展示上限
    request_limit: number | undefined; // 单设备请求上限
    show_limit: number | undefined; // 单设备展示上限
    virtual_request_limit: number | undefined; // 虚拟设备请求上限
    request_interval: number | undefined; // 单设备请求间隔
}

export type AdSlotSelection = {
    id : number;
    slot_code: number; // 广告位ID
    name: string; // 广告位名称
    ad_type: number; // 广告类型
};


export type AdSourceSelection = {
    id: number;
    name: string;
};

export function getAdSlotSelection4Media(selectAdMediaId : string): Promise<AdResult<AdSlotSelection[]>> {
    const value=apiClient.get(`${API_BASE}/slot/selection?media_id=${selectAdMediaId}`, {
        method: 'GET',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result(value)
}

export function  getAdSourceSelection4Slot(selectAdSlotId : string): Promise<AdResult<AdSourceSelection[]>> {
    const value=apiClient.get(`${API_BASE}/code/source_selection?slot_id=${selectAdSlotId}`, {
        method: 'GET',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<AdSourceSelection[]>(value)
}

export function querySlotList(page: number, pageSize: number, media_id?: string, ad_type?: string, enable?: boolean)
    : Promise<AdResult<AdSlotConfigList>> {
    let url = `${API_BASE}/slot/get?page=${page}&page_size=${pageSize}`
    if (media_id) {
        url += `&media_id=${media_id}`
    }
    if (ad_type) {
        url += `&ad_type=${ad_type}`
    }
    if (enable !== undefined) {
        url += `&enable=${enable}`
    }
    const value = apiClient.get(url, {
        method: 'GET',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<AdSlotConfigList>(value)
}

export function addSlot(slot: any): Promise<AdResult<any>> {
    const value = apiClient.post(`${API_BASE}/slot/add`, slot, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<any>(value)
}

export function updateSlot(slot: any): Promise<AdResult<any>> {
    const value = apiClient.post(`${API_BASE}/slot/update`, slot, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<any>(value)
}

export function toggleSlot(id: number, enable: boolean): Promise<AdResult<any>> {
    const value = apiClient.get(`${API_BASE}/slot/toggle?id=${id}&enable=${enable}`, {
        method: 'GET',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<any>(value)
}

export function removeSlot(id: number,slotId: number): Promise<AdResult<any>> {
    const value = apiClient.delete(`${API_BASE}/slot/delete?id=${id}&slot_id=${slotId}`, {
        method: 'DELETE',
        mode: 'cors',
        credentials: 'include',
    }).then((response) => response.json())
        .then((data) => data as AdResponse)
    return request2Result<any>(value)
}
