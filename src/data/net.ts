export const API_BASE = import.meta.env.VITE_API_BASE

export type AdResponse = {
    code: number;
    message: string;
    data: any;
};

export type AdResult<T> = {
    isSuccess: boolean;
    errMsg?: string;
    data?: T;
};

export type AdConfigList<T> = {
    list: T[];
    count: number;
};

export type AdConfigAndSelectList<T, S> = {
    list: T[];
    selectList: S[];
    select: S;
    count: number;
};

export async function request2Result<T>(request: Promise<AdResponse>): Promise<AdResult<T>> {
    try {
        const response = await request;
        if (response.code != 200) {
            return {
                isSuccess: false,
                errMsg: response.message,
            };
        }
        return {
            isSuccess: true,
            data: response.data,
        };
    } catch (error) {
        return {
            isSuccess: false,
            errMsg: error,
        };
    }
}