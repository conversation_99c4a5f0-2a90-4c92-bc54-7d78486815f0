import {eventBus} from "@/components/event/login";

export const API_BASE = import.meta.env.VITE_API_BASE

export type AdResponse = {
    code: number;
    message: string;
    data: any;
};

export type AdResult<T> = {
    isSuccess: boolean;
    code: number;
    errMsg?: string;
    data?: T;
};

export type AdConfigList<T> = {
    list: T[];
    count: number;
};

export type AdConfigAndSelectList<T, S> = {
    list: T[];
    selectList: S[];
    select: S;
    count: number;
};

export async function request2Result<T>(request: Promise<AdResponse>): Promise<AdResult<T>> {
    try {
        const response = await request;
        console.log('response', response)
        if (response.code === 200) {
            return {
                isSuccess: true,
                code: 0,
                data: response.data,
            };
        }
        return {
            isSuccess: false,
            code: response.code,
            errMsg: response.message,
        };
    } catch (error) {
        console.log('error', error)
        if (error.message === '401') {
            eventBus.emit('SHOW_LOGIN_MODAL');
            return {
                isSuccess: false,
                code: 401,
                errMsg: error,
            };
        }
        return {
            isSuccess: false,
            code: 500,
            errMsg: error,
        };
    }
}