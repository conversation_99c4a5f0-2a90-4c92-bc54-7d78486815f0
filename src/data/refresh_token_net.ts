// tokenManager.ts
import {message} from 'antd';

// Token存储键名
const TOKEN_KEY = 'access_token';
const REFRESH_TOKEN_KEY = 'refresh_token';
const EXPIRES_AT_KEY = 'token_expires_at';

// 全局状态
let isRefreshing = false;
let refreshSubscribers: ((token: string) => void)[] = [];

// 存储Token信息
export const setAuthTokens = (accessToken: string, refreshToken: string, expiresIn: number) => {
    const expiresAt = Date.now() + expiresIn * 1000;
    localStorage.setItem(TOKEN_KEY, accessToken);
    localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken);
    localStorage.setItem(EXPIRES_AT_KEY, expiresAt.toString());
};

// 获取Access Token
export const getAccessToken = (): string | null => {
    return localStorage.getItem(TOKEN_KEY);
};

// 获取Refresh Token
export const getRefreshToken = (): string | null => {
    return localStorage.getItem(REFRESH_TOKEN_KEY);
};

// 清除Token
export const clearAuthTokens = () => {
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(REFRESH_TOKEN_KEY);
    localStorage.removeItem(EXPIRES_AT_KEY);
};

// 检查Token是否过期
export const isTokenExpired = (): boolean => {
    const expiresAt = localStorage.getItem(EXPIRES_AT_KEY);
    if (!expiresAt) return true;

    // 添加5分钟缓冲期
    const buffer = 5 * 60 * 1000;
    return Date.now() >= (parseInt(expiresAt) - buffer);
};

// 刷新Token
const refreshToken = async (): Promise<void> => {
    const refreshToken = getRefreshToken();
    if (!refreshToken) {
        throw new Error('No refresh token available');
    }

    try {
        const response = await fetch('/user/refresh_token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${refreshToken}`
            }
        });

        if (!response.ok) {
            throw new Error(`Refresh failed with status ${response.status}`);
        }

        const data = await response.json();
        setAuthTokens(data.access_token, data.refresh_token, data.expires_in);
    } catch (error) {
        clearAuthTokens();
        message.error('会话已过期，请重新登录');
        // 实际项目中应跳转到登录页
        // history.push('/login');
        throw error;
    }
};

// 处理刷新过程中的请求
const subscribeTokenRefresh = (cb: (token: string) => void) => {
    refreshSubscribers.push(cb);
};

const onRefreshed = (token: string) => {
    refreshSubscribers.forEach(cb => cb(token));
    refreshSubscribers = [];
};

// 带Token刷新的Fetch请求
export const authFetch = async (input: RequestInfo, init?: RequestInit): Promise<Response> => {
    let accessToken = getAccessToken();

    // 初始请求
    const makeRequest = async (): Promise<Response> => {
        const headers = new Headers(init?.headers);
        if (accessToken) {
            headers.set('Authorization', `Bearer ${accessToken}`);
        }

        const response = await fetch(input, {
            ...init,
            headers
        });

        // 处理Token过期情况
        if (response.status === 601) {
            if (!isRefreshing) {
                isRefreshing = true;

                try {
                    await refreshToken();
                    accessToken = getAccessToken();
                    onRefreshed(accessToken!);
                } catch (error) {
                    refreshSubscribers = [];
                    throw error;
                } finally {
                    isRefreshing = false;
                }
            }

            // 等待刷新完成
            return new Promise((resolve) => {
                subscribeTokenRefresh((newToken: string) => {
                    headers.set('Authorization', `Bearer ${newToken}`);
                    resolve(fetch(input, {...init, headers}));
                });
            });
        }

        return response;
    };

    // 检查Token有效期
    if (isTokenExpired()) {
        if (!isRefreshing) {
            isRefreshing = true;

            try {
                await refreshToken();
                accessToken = getAccessToken();
                isRefreshing = false;
                return makeRequest();
            } catch (error) {
                isRefreshing = false;
                throw error;
            }
        } else {
            // 等待正在进行的刷新
            return new Promise((resolve) => {
                subscribeTokenRefresh(() => {
                    resolve(makeRequest());
                });
            });
        }
    }

    return makeRequest();
};

// API请求封装
export const apiClient = {
    get: (url: string, options?: RequestInit) =>
        authFetch(url, {method: 'GET', ...options}),

    post: (url: string, body: any, options?: RequestInit) =>
        authFetch(url, {
            method: 'POST',
            body: JSON.stringify(body),
            headers: {
                'Content-Type': 'application/json',
                ...options?.headers
            },
            ...options
        }),

    put: (url: string, body: any, options?: RequestInit) =>
        authFetch(url, {
            method: 'PUT',
            body: JSON.stringify(body),
            headers: {
                'Content-Type': 'application/json',
                ...options?.headers
            },
            ...options
        }),

    delete: (url: string, options?: RequestInit) =>
        authFetch(url, {method: 'DELETE', ...options})
};