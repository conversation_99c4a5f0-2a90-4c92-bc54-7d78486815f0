import { useEffect, useRef, useState } from 'react';
import {
  Table,
  Pagination,
  Button,
  Select,
  Switch,
  Row,
  Col,
  Divider,
  Space,

} from 'antd';
import { ColumnsType } from 'antd/es/table';
import ModifyAdSourceModal, { AdSourceModify, ModifyAdSourceShowProps } from './ModifyAdSourceModal';
import { AdBudgetIdentifier } from '@/pages/budget/budget-party/BudgetPartyManagement';
import { useParams } from 'react-router-dom';

export type AdSource = {
  // 广告源ID
  id: number;

  // 预算源名称
  name: string;

  // 预算方ID
  budget_identifier: number;

  // 系统类型
  os_type: number;

  // 广告类型
  ad_type: number;

  // 广告源ID
  budget_ad_id: string;

  // 返点比例
  rebate_ratio?: number;

  // 是否开启定向设置
  open_directional_setting: boolean;

  // 允许展示区域
  area_ids?: string;

  // 允许展示品牌
  brand_ids?: string;

  // 允许安装应用
  installed_app_ids?: string;

  // 不允许展示应用包名
  no_allow_package_names?: string;

  // 是否开启频控设置
  open_frequency_setting: boolean;

  // 广告源请求上限
  source_request_limit?: number;

  // 广告源展示上限
  source_show_limit?: number;

  // 单设备请求上限
  request_limit?: number;

  // 单设备展示上限
  show_limit?: number;

  //虚拟设备请求上限
  virtual_request_limit?: number;

  // 单设备请求间隔
  request_interval?: number;

  // 报备应用ID
  app_id?: string;

  //报备应用Key
  app_key?: string;

  // 报备应用名称
  app_name?: string;

  // 报备应用包名
  app_package_name?: string;

  // 报备起应用版本
  app_version_min?: string;

  // 报备止应用版本
  app_version_max?: string;

  // 广告启用状态
  enable: boolean;
};
export type AdDirectionalSetting = {
  // 允许展示区域
  area_ids?: string;

  // 允许展示品牌
  brand_ids?: string;

  // 允许安装应用
  installed_app_ids?: string;

  // 不允许展示应用包名
  no_allow_package_names?: string;
};
export type AdFrequencySetting = {
  // 广告源请求上限
  source_request_limit?: number;

  // 广告源展示上限
  source_show_limit?: number;

  // 单设备请求上限
  request_limit?: number;

  // 单设备展示上限
  show_limit?: number;

  //虚拟设备请求上限
  virtual_request_limit?: number;

  // 单设备请求间隔
  request_interval?: number;
};
export type AdReportSetting = {
  // 报备应用ID
  app_id?: string;

  //报备应用Key
  app_key?: string;

  // 报备应用名称
  app_name?: string;

  // 报备应用包名
  app_package_name?: string;

  // 报备起应用版本
  app_version_min?: string;

  // 报备止应用版本
  app_version_max?: string;
};

export type AdBudgetIdentifierSelect = {
    label: string;
    value: number;
}


const AdSourceManagement = () => {
  const [selectedPlatform, setSelectedPlatform] = useState<
    'Android' | 'iOS' | undefined
  >();
  const [selectedStatus, setSelectedStatus] = useState<true | false | undefined>();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(15);
  const [total, setTotal] = useState(100); // 模拟总数据量
  const [dataSource, setDataSource] = useState<AdSource[]>([]);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = currentPage * pageSize;
  const currentData = dataSource.slice(startIndex, endIndex);
  const [budgetIdentifier,setBudgetIdentifier] = useState<AdBudgetIdentifierSelect[]>([])
  const [refresh, setRefresh] = useState(false)
  const params = useParams();  
  console.log(params);
  const [selectBudgetIdentifier, setSelectBudgetIdentifier] = useState<string|undefined>(params['budget_identifier'])

  useEffect(() => {
    fetch("http://localhost:8090/budget/identifier", { method: 'GET', mode: 'cors' })
    .then((response) => response.json())
    .then((data) => {
      const result:AdBudgetIdentifierSelect[] = []
      data.data.forEach((item: AdBudgetIdentifier) => {
        result.push({label: item.name, value: item.budget_identifier})
      })
      setBudgetIdentifier(result)
      var url =`http://localhost:8090/source/get?page=${currentPage}&page_size=${pageSize}`
      if (selectBudgetIdentifier) {
        url+=`&budget_identifier=${selectBudgetIdentifier}`
      }
      if(selectedPlatform){
        url+=`&os_type=${selectedPlatform === 'Android' ? 1 : 2}`
      }
      fetch(url, { method: 'GET', mode: 'cors' })
        .then((response) => response.json())
        .then((data) => {
          const count=data.data.count
          const sourceList = data.data.list
          setDataSource(sourceList);  
          setTotal(count);
        })
        .catch((error) => {
          console.error('Error fetching data:', error);
        });
    }).catch((error) => {
        console.error('Error fetching data:', error);
      });
  },[refresh,selectBudgetIdentifier])

  const [showProps, setShowProps] = useState<ModifyAdSourceShowProps>({
    visible: false, // 控制弹窗显示
  })
  const handleAddAdSource = () => {
    setShowProps({ visible: true })
  };
  const handleCreate = (values: AdSourceModify, isEdit: boolean, dataSource?: AdSource) => {
    setShowProps({ visible: false })
    let ad_directional_setting = undefined
    if (values.area_ids || values.brand_ids || values.installed_app_ids || values.no_allow_package_names) {
      ad_directional_setting = {
        area_ids: values.area_ids,
        brand_ids: values.brand_ids,
        installed_app_ids: values.installed_app_ids,
        no_allow_package_names: values.no_allow_package_names,
      }
    }
    let ad_frequency_setting = undefined
    if (values.source_request_limit || values.source_show_limit || values.request_limit || values.show_limit || values.request_interval|| values.virtual_request_limit) {
      ad_frequency_setting = {
        source_request_limit: Number(values.source_request_limit),
        source_show_limit: Number(values.source_show_limit),
        request_limit: Number(values.request_limit),
        show_limit: Number(values.show_limit),
        request_interval: Number(values.request_interval),
        virtual_request_limit: Number(values.virtual_request_limit),
      }
    }
    let ad_report_setting = undefined
    if (values.app_id|| values.app_key || values.app_name || values.app_package_name || values.app_version_min || values.app_version_max) {
      ad_report_setting = {
        app_id: values.app_id,
        app_key: values.app_key,
        app_name: values.app_name,
        app_package_name: values.app_package_name,
        app_version_min: values.app_version_min,
        app_version_max: values.app_version_max,
      }
    }
    if (isEdit) {
      // 编辑
      const updateBudget = {
        id: dataSource?.id,
        name: values.name,
        rebate_ratio: Number(values.rebate_ratio),
        open_directional_setting: values.open_directional_setting,
        ad_directional_setting: ad_directional_setting,
        open_frequency_setting: values.open_frequency_setting,
        ad_frequency_setting: ad_frequency_setting,
        ad_report_setting: ad_report_setting,
      }
      fetch(`http://localhost:8090/source/update`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(updateBudget), mode: 'cors' })
        .then((response) => {
          if (response.status !== 200) {
            throw new Error('')
          }
          return response
        })
        .then((response) => response.json())
        .then((data) => {
          alert('编辑成功')
          setRefresh(true)
        })
        .catch((error) => {
          console.error('Error fetching data:', error);
          alert('编辑失败')
        });
    } else {
      // 新增
      const createBudget = {
        name: values.name,
        budget_identifier: values.budget_identifier,
        os_type: values.os_type,
        ad_type: values.ad_type,
        rebate_ratio: Number(values.rebate_ratio),
        budget_ad_id: values.budget_ad_id,
        open_directional_setting: values.open_directional_setting,
        ad_directional_setting: ad_directional_setting,
        open_frequency_setting: values.open_frequency_setting,
        ad_frequency_setting: ad_frequency_setting,
        ad_report_setting: ad_report_setting,
        enable: true
      }
      fetch(`http://localhost:8090/source/add`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(createBudget), mode: 'cors' })
        .then((response) => {
          if (response.status !== 200) {
            throw new Error('')
          }
          return response
        })
        .then((response) => response.json())
        .then((data) => {
          alert('新增成功')
          setRefresh(true)
        }).catch((error) => {
          console.error('Error fetching data:', error);
          alert('新增失败')
        });
    }
  };

  const handleOnEditClick = (record: AdSource) => {
    setShowProps({ visible: true, dataSource: record })
  }

  const handleOnDeleteClick = (record: AdSource) => {
      fetch(`http://localhost:8090/source/delete?id=${record.id}` , { method: 'GET', mode: 'cors' })
      .then((response) => {
          if (response.status!== 200) {
            throw new Error('')
          }
          return response
        })
      .then((response) => response.json())
      .then((data) => {
          alert('删除成功')
          setRefresh(true)
      })
     .catch((error) => {
          console.error('Error fetching data:', error);
          alert('删除失败')
      });
  }

  const budgetIdentifier2Name = (budget_identifier: number) => {
    const budget = budgetIdentifier.find((budget) => budget.value === budget_identifier)
    return budget?.label || '未知'
  }

  // 表格列定义
  const columns: ColumnsType<AdSource> = [
    {
      title: '广告源',
      dataIndex: 'name',
      key: 'name',
      render: (text) => <div style={{ maxWidth: 200, overflow: 'auto' }}>{text}</div>,
    },
    {
      title: '预算方名称',
      dataIndex: 'budget_identifier',
      key: 'budget_identifier',
      render: (budget_identifier: number) => {
        const text = budgetIdentifier2Name(budget_identifier)
        return <div style={{ maxWidth: 200, overflow: 'auto' }}>{text}</div>
      },
    },
    {
      title: '平台类型',
      dataIndex: 'os_type',
      key: 'os_type',
      render: (os_type: number) => {
        const text = os_type === 1 ? 'Android' : os_type === 2 ? 'iOS' : '未知'
        return <div style={{ maxWidth: 200, overflow: 'auto' }}>{text}</div>
      },
    },
    {
      title: '广告类型',
      dataIndex: 'ad_type',
      key: 'ad_type',
      render: (ad_type: number) => {
        const text = ad_type === 1 ? '开屏广告' : ad_type === 2 ? '插屏广告' : ad_type === 3 ? '原生广告' : ad_type === 4 ? '激励视频广告' : ad_type === 5 ? '横幅广告' : '未知'
        return <div style={{ maxWidth: 200, overflow: 'auto' }}>{text}</div>
      },
    },
    {
      title: '是否启动',
      key: 'enable',
      dataIndex: 'enable',
      render: (status, record: AdSource) => <Switch checked={status} onChange={(checked) => { handleEnableChange(record.id, checked) }} />, // 模拟不可编辑状态
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: AdSource) => (
        <Space size="small">
          <a onClick={() => { handleOnEditClick(record) }}>编辑</a>
          <a onClick={() => { handleOnDeleteClick(record) }}>删除</a>
        </Space>
      ),
    },
  ];

  // 分页处理
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  const handleEnableChange = (id: number, checked: boolean) => {
    fetch(`http://localhost:8090/source/toggle?id=${id}&enable=${checked}`,
      { method: 'GET', mode: 'cors' })
      .then((response) => {
        if (response.status !== 200) {
          throw new Error('')
        }
        return response
      })
      .then((response) => response.json())
      .then((data) => {
        setRefresh(true)
      }).catch((error) => {
        console.error('Error fetching data:', error);
        alert('修改失败')
      });
  }

  return (
    <div style={{ padding: '8px 24px 24px 24px', minHeight: '100vh' }}>
      {/* 顶部操作区 */}
      <div style={{
        background: '#fff',
        border: '1px solid #f0f0f0',
        borderRadius: 8,
        padding: 16,
        marginBottom: 16,
        overflow: 'hidden'
      }}>
        <Row justify="space-between" align="middle" gutter={24}>
          <Col>
            <span style={{
              marginRight: 24,
              fontWeight: '500',
              fontSize: '15px'
            }}>预算方类型: </span>
            <Select
              placeholder="请选择预算方类型"
              style={{ width: 240, marginRight: 24, }}
              value={selectBudgetIdentifier}
              onChange={(value) => setSelectBudgetIdentifier(value)}
              options={budgetIdentifier}
            />

            <span style={{
              marginRight: 24,
              fontWeight: '500',
              fontSize: '15px'
            }}>平台类型: </span>
            <Select
              placeholder="请选择平台类型"
              style={{ width: 240, marginRight: 24, }}
              value={selectedPlatform}
              onChange={(value) => setSelectedPlatform(value)}
              options={[
                { value: 'Android', label: 'Android' },
                { value: 'iOS', label: 'iOS' },
              ]}
            />
            <span style={{
              marginRight: 24,
              fontWeight: '500',
              fontSize: '15px'
            }}>是否启动: </span>

            <Select
              placeholder="是否启用"
              style={{ width: 120 }}
              value={selectedStatus}
              onChange={(value) => setSelectedStatus(value)}
              options={[{ value: true, label: '启用' }, { value: false, label: '关闭' }]}
            />
          </Col>
          <Col>
            <Space size="middle">
              <Button onClick={() => {/* 待实现重置逻辑 */ }}>重置</Button>
              <Button type="primary" onClick={() => {/* 待实现搜索逻辑 */ }}>搜索</Button>
            </Space>
          </Col>
        </Row>
      </div>

      {/* 表格区域 */}
      <div style={{
        overflow: 'auto', height: 'calc(100vh - 300px)', background: '#fff',
        border: '1px solid #f0f0f0',
        borderRadius: 8,
        padding: 16,
      }}>
        <Button type="primary" style={{ marginRight: 16 }} onClick={handleAddAdSource}>
          添加
        </Button>
        <ModifyAdSourceModal
          show={showProps}
          adBudgetIdentifierList={budgetIdentifier}
          onCancel={() => {
            setShowProps({ visible: false })
          }}
          onConfirm={handleCreate}
        />
        <Table
          columns={columns}
          dataSource={currentData}
          rowKey="id"
          pagination={false} // 使用自定义分页
        />
      </div>

      {/* 分页区 */}
      <Divider style={{ margin: '24px 0' }} />
      <Pagination
        current={currentPage}
        pageSize={pageSize}
        total={total}
        onChange={handlePageChange}
        onShowSizeChange={handlePageSizeChange}
        pageSizeOptions={['15', '30', '50']}
        showSizeChanger
        showQuickJumper
        showTotal={(total) => `共 ${total} 条`}
        style={{ float: 'right' }}
      />
    </div>
  );
};

export default AdSourceManagement;
