import { useEffect, useState } from 'react';
import { Modal, Switch, Form, Input, Select, Button, Typography } from 'antd';
import { AdBudgetIdentifierSelect, AdDirectionalSetting, AdFrequencySetting, AdReportSetting, AdSource } from './AdSourceManagement';
import { AdBudgetIdentifier } from '../budget-party/BudgetPartyManagement';

export type ModifyAdSourceShowProps = {
  visible: boolean; // 控制弹窗显示
  dataSource?: AdSource
}

export type AdSourceModify = {
  // 预算源名称
  name: string;

  // 预算方ID
  budget_identifier: number;

  // 系统类型
  os_type: number;

  // 广告类型
  ad_type: number;

  // 广告源ID
  budget_ad_id: string;

  // 返点比例
  rebate_ratio?: number;

  // 是否开启定向设置
  open_directional_setting: boolean;

  // 允许展示区域
  area_ids?: string;

  // 允许展示品牌
  brand_ids?: string;

  // 允许安装应用
  installed_app_ids?: string;

  // 不允许展示应用包名
  no_allow_package_names?: string;

  // 是否开启频控设置
  open_frequency_setting: boolean;

  // 广告源请求上限
  source_request_limit?: number;

  // 广告源展示上限
  source_show_limit?: number;

  // 单设备请求上限
  request_limit?: number;

  // 单设备展示上限
  show_limit?: number;

  // 单设备请求间隔
  request_interval?: number;

  //虚拟设备请求上限
  virtual_request_limit?: number;

  // 报备应用ID
  app_id?: string;

  // 报备应用KEY
  app_key?: string;

  // 报备应用名称
  app_name?: string;

  // 报备应用包名
  app_package_name?: string;

  // 报备起应用版本
  app_version_min?: string;

  // 报备止应用版本
  app_version_max?: string;

  // 广告启用状态
  enable: boolean;
};

type ModifyAdSourceModalProps = {
  show: ModifyAdSourceShowProps; // 控制弹窗显示
  adBudgetIdentifierList: AdBudgetIdentifierSelect[];
  onConfirm: (values: AdSourceModify, isEdit: boolean, dataSource?: AdSource) => void; // 创建预算方的回调函数
  onCancel: () => void;
}

const OS_OPTIONS = [
  { value: 1, label: 'Android' },
  { value: 2, label: 'iOS' },
]

const AD_TYPE_OPTIONS = [
  { value: 1, label: '开屏广告' },
  { value: 2, label: '插屏广告' },
  { value: 3, label: '原生广告' },
  { value: 4, label: '激励视频广告' },
  { value: 5, label: '横幅广告' },
]

const ModifyAdSourceModal = ({ show, adBudgetIdentifierList, onConfirm, onCancel }: ModifyAdSourceModalProps) => {
  const [form] = Form.useForm();
  const { visible, dataSource } = show;
  const isEdit = dataSource != undefined
  // const [directionSettings, setDirectionSettings] = useState(false);
  const [frequencySettings, setFrequencySettings] = useState(false);

  useEffect(() => {
    console.log('show', show);
    const { visible, dataSource } = show;
    if (visible && dataSource) {
      form.resetFields();
      form.setFieldsValue({ ...dataSource })
    } else if (visible) {
      console.log('重置表单');
      form.resetFields();
    }
  }, [show])

  const handleOk = () => {
    form.validateFields()
      .then(values => {
        onConfirm(values, isEdit, dataSource);
      })
      .catch(err => console.log('表单验证错误', err));
  };

  const readonlyStyle = {
    backgroundColor: '#f5f5f5',
    padding: '4px 8px',
    borderRadius: 4,
    color: '#666',
    cursor: 'not-allowed',
    display: 'inline-block',
    width: '100%' // 保持和Select相同宽度
  };

  const renderIdentifierField = (
    <Form.Item
      label="预算方"
      name="budget_identifier"
      rules={[{ required: !isEdit, message: isEdit ? undefined : '请选择预算方类型' }]}
    >
      {isEdit ? (
        <Typography.Text code style={readonlyStyle}>
          {adBudgetIdentifierList.find(opt => opt.value === dataSource.budget_identifier)?.label || '未知类型'}
        </Typography.Text>
      ) : (
        <Select
          placeholder="请选择预算方"
          onChange={(value) => {
            form.setFieldsValue({
              budget_identifier: value,
            });
          }}
        >
          {adBudgetIdentifierList.map(option => (
            <Select.Option key={option.value} value={option.value}>
              {option.label}
            </Select.Option>
          ))}
        </Select>
      )}
    </Form.Item>
  );

  const renderBudgetAdIdField = (
    <Form.Item
      label="绑定预算方平台广告位ID"
      name="budget_ad_id"
      rules={[{ required: true, message: '请输入2~100位字符' }]}
    >
      {isEdit ? (
        <Typography.Text code style={readonlyStyle}>
          {dataSource.budget_ad_id || '未绑定'}
        </Typography.Text>
      ) : (
        <Input placeholder="请输入2~100位字符" />
      )}
    </Form.Item>
  );

  const rederOSField = (
    <Form.Item
      label="系统类型"
      name="os_type"
      rules={[{ required: true, message: '请选择系统类型' }]}
    >
      {isEdit ? (
        <Typography.Text code style={readonlyStyle}>
          {OS_OPTIONS.find(opt => opt.value === dataSource.os_type)?.label || '未知类型'}
        </Typography.Text>
      ) : (
        <Select
          placeholder="请选择系统类型"
          onChange={(value) => {
            form.setFieldsValue({
              os_type: value,
            });
          }}
        >
          {OS_OPTIONS.map(option => (
            <Select.Option key={option.value} value={option.value}>
              {option.label}
            </Select.Option>
          ))}
        </Select>
      )}
    </Form.Item>
  )

  const renderAdTypeField = (
    <Form.Item
      label="广告位类型"
      name="ad_type"
      rules={[{ required: true, message: '请选择广告位类型' }]}
    >
      {isEdit ? (
        <Typography.Text code style={readonlyStyle}>
          {AD_TYPE_OPTIONS.find(opt => opt.value === dataSource.ad_type)?.label || '未知类型'}
        </Typography.Text>
      ) : (
        <Select
          placeholder="请选择广告位类型"
          onChange={(value) => {
            form.setFieldsValue({
              ad_type: value,
            });
          }}
        >
          {AD_TYPE_OPTIONS.map(option => (
            <Select.Option key={option.value} value={option.value}>
              {option.label}
            </Select.Option>
          ))}
        </Select>
      )}
    </Form.Item>
  )

  return (
    <Modal
      destroyOnClose={true}
      title={isEdit ? '编辑预算方' : '创建预算方'}
      open={visible}
      onCancel={onCancel}
      width={640}
      footer={[
        <Button key="back" onClick={onCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={handleOk}>
          确定
        </Button>,
      ]}
    // style={{ height: '100vh', overflow: 'auto' }}
    >
      <Form form={form} layout="vertical" style={{ padding: 24 }}
        initialValues={dataSource}>
        {renderIdentifierField}
        <Form.Item
          label="广告源名称"
          name="name"
          rules={[{ required: true, message: '请输入广告源名称' }]}>
          <Input placeholder="请输入广告源名称" />
        </Form.Item>
        {rederOSField}
        {renderAdTypeField}
        <Form.Item
          label="返点比例"
          name="rebate_ratio"
          rules={[{ required: false, message: '请输入1~100整数', pattern: /^[1-9]\d?$|^100$/, }]}
        >
          <Input placeholder="请输入1~100整数" type="number" min={1}
            max={100} />
        </Form.Item>
        {renderBudgetAdIdField}
        {/* <Form.Item label="定向设置" name="open_directional_setting">
          <Switch checked={directionSettings} onChange={setDirectionSettings} />
        </Form.Item>
        {directionSettings && (
          <>
            <Form.Item label="地域" name="area_ids" initialValue=''>
              <Select placeholder="包含">
                <Option value="option1">选项1</Option>
                <Option value="option2">选项2</Option>
              </Select>
            </Form.Item>
            <Form.Item label="制造商" name="brand_ids" initialValue=''>
              <Select placeholder="包含">
                <Option value="option1">选项1</Option>
                <Option value="option2">选项2</Option>
              </Select>
            </Form.Item>
            <Form.Item label="用户已安装包名" name="installed_app_ids" initialValue=''>
              <Select placeholder="包含">
                <Option value="option1">选项1</Option>
                <Option value="option2">选项2</Option>
              </Select>
            </Form.Item>
            <Form.Item label="不展示包名(每行一个)" name="no_allow_package_names" initialValue=''>
              <Input.TextArea placeholder="请输入不展示包名" />
            </Form.Item>
          </>
        )} */}
        <Form.Item name="open_frequency_setting" label="频次设置">
          <Switch checked={frequencySettings} onChange={setFrequencySettings} />
        </Form.Item>
        {frequencySettings && (
          <>
            <Form.Item
              label="每日上限"
              wrapperCol={{ span: 24 }} // 占据全部宽度
            >
              <Form.Item
                initialValue=''
                name="source_request_limit"
                label="请求"
                rules={[{ required: true, message: '请输入0-99亿之间的整数' }]}
                style={{ display: 'inline-block', width: '45%' }} // 调整宽度
              >
                <Input placeholder="请输入0-99亿之间的整数" type="number"  // 设置 HTML5 数字输入
                  min={1}
                  max={999999999} />
              </Form.Item>
              <Form.Item
                initialValue=''
                name="source_show_limit"
                label="展现"
                rules={[{ required: true, message: '请输入0-99亿之间的整数' }]}
                style={{ display: 'inline-block', width: '45%', marginLeft: '10%' }} // 调整宽度和间距
              >
                <Input placeholder="请输入0-99亿之间的整数" type="number"  // 设置 HTML5 数字输入
                  min={1}
                  max={999999999} />
              </Form.Item>
            </Form.Item>
            <Form.Item
              label="单设备每日上限"
              wrapperCol={{ span: 24 }} // 占据全部宽度
            >
              <Form.Item
                initialValue=''
                name="request_limit"
                label="请求"
                rules={[{ required: true, message: '请输入0-999之间的整数' }]}
                style={{ display: 'inline-block', width: '45%' }} // 调整宽度
              >
                <Input placeholder="请输入0-99亿之间的整数" type="number"  // 设置 HTML5 数字输入
                  min={1}
                  max={999} />
              </Form.Item>
              <Form.Item
                initialValue=''
                name="show_limit"
                label="展现"
                rules={[{ required: true, message: '请输入0-999之间的整数' }]}
                style={{ display: 'inline-block', width: '45%', marginLeft: '10%' }} // 调整宽度和间距
              >
                <Input placeholder="请输入0-99亿之间的整数" type="number"  // 设置 HTML5 数字输入
                  min={1}
                  max={999} />
              </Form.Item>
            </Form.Item>
            <Form.Item
              label="单设备最小请求间隔"
              wrapperCol={{ span: 24 }}
              initialValue='' // 占据全部宽度
            >
              <Form.Item
                name="request_interval"
                rules={[{ required: false, message: '请输入0-999之间的...秒' }]}
                style={{ width: '45%' }} // 调整宽度
              >
                <Input placeholder="请输入0-99999之间的...秒" type="number"  // 设置 HTML5 数字输入
                  min={1}
                  max={999} />
              </Form.Item>
            </Form.Item>


            <Form.Item
              label="虚拟设备请求上限"
              wrapperCol={{ span: 24 }}
              initialValue='' // 占据全部宽度
            >
              <Form.Item
                name="virtual_request_limit"
                rules={[{ required: false, message: '请输入0-99亿之间的整数' }]}
                style={{ width: '45%' }} // 调整宽度
              >
                <Input placeholder="请输入0-99亿之间的整数" type="number"  // 设置 HTML5 数字输入
                  min={1}
                  max={999} />
              </Form.Item>
            </Form.Item>
          </>
        )}
        <Form.Item name="app_id" label="广告主应用ID" initialValue=''>
          <Input placeholder="请输入广告主应用ID" />
        </Form.Item>
        <Form.Item name="app_key" label="广告主应用KEY" initialValue=''>
          <Input placeholder="请输入广告主应用KEY" />
        </Form.Item>
        <Form.Item name="app_name" label="报备应用名称" initialValue=''>
          <Input placeholder="请输入报备应用名称" />
        </Form.Item>
        <Form.Item name="app_package_name" label="报备应用包名" initialValue=''>
          <Input placeholder="请输入报备应用包名" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ModifyAdSourceModal;