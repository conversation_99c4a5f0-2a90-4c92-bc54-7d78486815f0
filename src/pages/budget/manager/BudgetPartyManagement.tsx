import {useEffect, useRef, useState} from 'react';
import {
    Table,
    Pagination,
    Button,
    Select,
    Switch,
    Row,
    Col,
    Divider,
    Space,

} from 'antd';
import ModifyBudgetModal, {ModifyBudgetShowProps} from './ModifyBudgetModal';
import {ColumnsType} from 'antd/es/table';
import {useNavigate} from 'react-router-dom';
import {
    AdBudget,
    AdBudgetIdentifier, AdBudgetModify,
    add, queryBudgetIdentifier,
    queryBudgetList,
    remove,
    toggle,
    update
} from "@/data/budget_manager/budget";


const BudgetPartyManagement = () => {
    const [selectedStatus, setSelectedStatus] = useState<true | false | undefined>();
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(15);
    const [total, setTotal] = useState(100); // 模拟总数据量
    const [dataSource, setDataSource] = useState<AdBudget[]>([]);
    const budgetIdentifier = useRef<AdBudgetIdentifier[]>([])
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = currentPage * pageSize;
    const currentData = dataSource.slice(startIndex, endIndex);
    const [refresh, setRefresh] = useState(false)
    const navigate = useNavigate()

    useEffect(() => {
        queryBudgetList(currentPage, pageSize).then(value => {
            if (!value.isSuccess) {
                 if (value.code!=401){
                     alert('获取预算方失败')
                 }
                return
            }
            const data = value.data
            if (!data) {
                alert('获取预算方失败')
                return
            }
            const list = data.list
            if (!list) {
                alert('获取预算方失败')
                return
            }
            setDataSource(list)
            setTotal(data.count)
        })

        queryBudgetIdentifier().then(value => {
            if (!value.isSuccess) {
                if (value.code!=401){
                    alert('获取预算方失败')
                }
                return
            }
            const data = value.data
            if (!data) {
                alert('获取预算方失败')
                return
            }
            budgetIdentifier.current = data
        })
    }, [refresh])
    const [showProps, setShowProps] = useState<ModifyBudgetShowProps>({
        visible: false, // 控制弹窗显示
    })
    const handleAddBudget = () => {
        setShowProps({visible: true})
    };
    const handleCreate = (values: AdBudgetModify, isEdit: boolean, dataSource?: AdBudget) => {
        setShowProps({visible: false})
        if (isEdit) {
            const updateBudget = {
                id: dataSource?.id,
                budget_identifier: values.budget_identifier,
                name: values.name,
                company_name: values.company_name,
                contact: values.contact,
                phone: values.phone,
            }
            update(updateBudget).then(value => {
                if (value.isSuccess) {
                    setRefresh(true)
                } else {
                    if (value.code!=401){
                        alert('编辑失败')
                    }
                }
            })
        } else {
            // 新增
            const createBudget = {
                budget_identifier: values.budget_identifier,
                name: values.name,
                company_name: values.company_name,
                contact: values.contact,
                phone: values.phone,
            }
            add(createBudget).then(value => {
                if (value.isSuccess) {
                    setRefresh(true)
                } else {
                    if (value.code!=401){
                        alert('新增失败')
                    }
                }
            })
        }
    };
    const handleOnEditClick = (record: AdBudget) => {
        setShowProps({visible: true, dataSource: record})
    }
    const handleOnDeleteClick = (record: AdBudget) => {
        remove(record.id).then(value => {
            {
                if (value.isSuccess) {
                    setRefresh(true)
                } else {
                    if (value.code!=401){
                        alert('删除失败')
                    }
                }
            }
        })
    }

    const handleOnCreateSource = (record: AdBudget) => {
        navigate(`/budgetManager/adSource?budget_identifier=${record.budget_identifier}`)
    }

    // 表格列定义
    const columns: ColumnsType<AdBudget> = [
        {
            title: '流量方名称',
            dataIndex: 'name',
            key: 'name',
            render: (text) => <div style={{maxWidth: 200, overflow: 'auto'}}>{text}</div>,
        },
        {
            title: '公司名称',
            dataIndex: 'company_name',
            key: 'company_name',
            render: (text) => <div style={{maxWidth: 200, overflow: 'auto'}}>{text}</div>,
        },
        {
            title: '联系人',
            dataIndex: 'contact',
            key: 'contact',
            render: (text) => <div style={{maxWidth: 200, overflow: 'auto'}}>{text}</div>,
        },
        {
            title: '联系电话',
            dataIndex: 'phone',
            key: 'phone',
            render: (text) => <div style={{maxWidth: 200, overflow: 'auto'}}>{text}</div>,
        },
        {
            title: '是否启动',
            key: 'enable',
            dataIndex: 'enable',
            render: (status, record: AdBudget) => <Switch checked={status} onChange={(checked) => {
                handleEnableChange(record.id, checked)
            }}/>, // 模拟不可编辑状态
        },
        {
            title: '操作',
            key: 'action',
            render: (_, record: AdBudget) => (
                <Space size="small">
                    <a onClick={() => {
                        handleOnEditClick(record)
                    }}>编辑</a>
                    <a onClick={() => {
                        handleOnDeleteClick(record)
                    }}>删除</a>
                    <a onClick={() => {
                        handleOnCreateSource(record)
                    }}>创建广告源</a>
                </Space>
            ),
        },
    ];

    // 分页处理
    const handlePageChange = (page: number) => {
        setCurrentPage(page);
    };

    const handlePageSizeChange = (size: number) => {
        setPageSize(size);
        setCurrentPage(1);
    };

    const handleEnableChange = (id: number, checked: boolean) => {
        toggle(id, checked).then(value => {
            if (value.isSuccess) {
                setRefresh(true)
            } else {
                if (value.code!=401) {
                    if (checked) {
                        alert('启动失败')
                    } else {
                        alert('关闭失败')
                    }
                }
            }
        })
    }

    return (
        <div style={{padding: '8px 24px 24px 24px', minHeight: '100vh'}}>
            {/* 顶部操作区 */}
            <div style={{
                background: '#fff',
                border: '1px solid #f0f0f0',
                borderRadius: 8,
                padding: 16,
                marginBottom: 16,
                overflow: 'hidden'
            }}>
                <Row justify="space-between" align="middle" gutter={24}>
                    <Col>
            <span style={{
                marginRight: 24,
                fontWeight: '500',
                fontSize: '15px'
            }}>是否启动：</span>

                        <Select
                            placeholder="是否启用"
                            style={{width: 120}}
                            value={selectedStatus}
                            onChange={(value) => setSelectedStatus(value)}
                            options={[{value: true, label: '启用'}, {value: false, label: '关闭'}]}
                        />
                    </Col>
                    <Col>
                        <Space size="middle">
                            <Button onClick={() => {/* 待实现重置逻辑 */
                            }}>重置</Button>
                            <Button type="primary" onClick={() => {/* 待实现搜索逻辑 */
                            }}>搜索</Button>
                        </Space>
                    </Col>
                </Row>
            </div>


            {/* 表格区域 */}
            <div style={{
                overflow: 'auto', height: 'calc(100vh - 300px)', background: '#fff',
                border: '1px solid #f0f0f0',
                borderRadius: 8,
                padding: 16,
            }}>
                <Button type="primary" style={{marginRight: 16, marginBottom: 16}} onClick={handleAddBudget}>
                    添加
                </Button>
                <ModifyBudgetModal
                    show={showProps}
                    adBudgetIdentifierList={budgetIdentifier.current}
                    onCancel={() => {
                        setShowProps({visible: false})
                    }}
                    onConfirm={handleCreate}
                />
                <Table
                    columns={columns}
                    dataSource={currentData}
                    rowKey="id"
                    pagination={false} // 使用自定义分页
                    scroll={{y: 'calc(100vh - 400px)'}} // 表格内容滚动高度
                />
            </div>

            {/* 分页区 */}
            <Divider style={{margin: '24px 0'}}/>
            <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={total}
                onChange={handlePageChange}
                onShowSizeChange={handlePageSizeChange}
                pageSizeOptions={['15', '30', '50']}
                showSizeChanger
                showQuickJumper
                showTotal={(total) => `共 ${total} 条`}
                style={{float: 'right'}}
            />
        </div>
    );
};

export default BudgetPartyManagement;

