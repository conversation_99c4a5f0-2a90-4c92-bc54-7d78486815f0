import { Modal, Form, Input, Select, Button, Typography } from 'antd';
import { useEffect } from 'react';
import {AdBudget, AdBudgetIdentifier, AdBudgetModify} from "@/data/budget_manager/budget";

export  type ModifyBudgetShowProps = {
    visible: boolean; // 控制弹窗显示
    // 编辑时的数据源
    dataSource?: AdBudget
}

type ModifyBudgetModalProps = {
    show: ModifyBudgetShowProps; // 控制弹窗显示
    adBudgetIdentifierList:AdBudgetIdentifier[];
    onConfirm: (values: AdBudgetModify, isEdit: boolean, dataSource?: AdBudget) => void; // 创建预算方的回调函数
    onCancel: () => void;
}


const ModifyBudgetModal = ({ show,adBudgetIdentifierList, onConfirm, onCancel }: ModifyBudgetModalProps) => {
    const [form] = Form.useForm<AdBudgetModify>();
    const {visible,dataSource} = show;
    const isEdit = dataSource != undefined

    useEffect(() => {
        console.log('show',show);
        const {visible,dataSource} = show;
        if (visible&&dataSource) {
            form.resetFields();
            form.setFieldsValue({ ...dataSource })
        } else if(visible){
            console.log('重置表单');
            form.resetFields();
        }
    }, [show])

    // 表单提交处理
    const handleSubmit = () => {
        form.validateFields()
            .then(values => {
                onConfirm(values, isEdit, dataSource);
            })
            .catch(err => console.log('表单验证错误', err));
    };

    const readonlyStyle = {
        backgroundColor: '#f5f5f5',
        padding: '4px 8px',
        borderRadius: 4,
        color: '#666',
        cursor: 'not-allowed',
        display: 'inline-block',
        width: '100%' // 保持和Select相同宽度
    };

    const renderBudgetNameField = (
        <Form.Item
            label="* 预算方"
            name="budget_identifier"
            rules={[{ required: !isEdit, message: isEdit ? undefined : '请选择预算方' }]}
        >
            {isEdit ? (
                <Typography.Text code style={readonlyStyle}>
                    {adBudgetIdentifierList.find(opt => opt.budget_identifier === dataSource.budget_identifier)?.name || '未知类型'}
                </Typography.Text>
            ) : (
                <Select
                    placeholder="请选择预算方"
                    onChange={(value) => {
                        const selected = adBudgetIdentifierList.find(opt => opt.budget_identifier === value);
                        form.setFieldsValue({
                            budget_identifier: value,
                            name: selected?.name // 同时设置 name 字段
                        });
                    }}
                >
                    {adBudgetIdentifierList.map(option => (
                        <Select.Option key={option.budget_identifier} value={option.budget_identifier}>
                            {option.name}
                        </Select.Option>
                    ))}
                </Select>
            )}
        </Form.Item>
    );

    return (
        <Modal
            destroyOnClose={true}
            title={isEdit ? '编辑预算方' : '创建预算方'}
            open={visible}
            onCancel={onCancel} // 取消创建的回调函
            footer={null} // 自定义底部按钮
            width={520}
        >
            <Form
                form={form}
                layout="vertical"
                style={{ padding: 24 }}
                initialValues={dataSource}
            >
                {renderBudgetNameField}

                <Form.Item name="name" hidden>
                    <Input />
                </Form.Item>

                <Form.Item
                    label="* 企业名称"
                    name="company_name"
                    rules={[{
                        required: true,
                        message: '请输入企业名称',
                        min: 2,
                        max: 50,
                        pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_-]{2,50}$/,
                        whitespace: true,
                    }]}
                >
                    <Input placeholder="请输入2-50位字符" />
                </Form.Item>

                <Form.Item
                    label="* 联系人"
                    name="contact"
                    rules={[{
                        required: true,
                        message: '请输入联系人',
                        min: 2,
                        max: 50,
                        pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_-]{2,50}$/,
                    }]}
                >
                    <Input placeholder="请输入2-50位字符" />
                </Form.Item>

                <Form.Item
                    label="* 联系电话"
                    name="phone"
                    rules={[{
                        required: true,
                        message: '请输入正确的手机号',
                        pattern: /^1[3-9]\d{9}$/,
                    }]}
                >
                    <Input placeholder="请输入手机号" />
                </Form.Item>

                <div style={{ textAlign: 'right', padding: '16px 24px 24px' }}>
                    <Button onClick={onCancel} style={{ marginRight: 8 }}>
                        取消
                    </Button>
                    <Button type="primary" onClick={handleSubmit}>
                        确定
                    </Button>
                </div>
            </Form>
        </Modal>
    );
};

export default ModifyBudgetModal;