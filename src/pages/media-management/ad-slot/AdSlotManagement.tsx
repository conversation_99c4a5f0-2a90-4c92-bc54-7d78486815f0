import React, { useEffect, useState } from 'react';
import {
  Table,
  Pagination,
  Button,
  Select,
  Switch,
  Space,
  Row,
  Col,
  Divider,
} from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useNavigate, useParams } from 'react-router-dom';
import ModifyAdSlotModal, { ModifyAdSlotShowProps } from './ModifyAdSlotModal';
import {
  addSlot,
  AdSlotCreateBody,
  AdSlotModel,
  AdSlotModify,
  AdSlotUpdateBody, querySlotList, removeSlot, toggleSlot, updateSlot
} from "@/data/flow_manager/slot";
import {AdFrequencySetting} from "@/data/common";
import {AdFlowSelect} from "@/pages/media-management/media/MediaManagement";
import {AdMediaSelection, getMediaSelection4Flow} from "@/data/flow_manager/media";
import {AdFlowSelection} from "@/data/flow_manager/flow";

export type AdMediaSelect = {
  label: string;
  value: string;
}

export const AD_TYPE_OPTIONS = [
  { value: 1, label: '开屏广告' },
  { value: 2, label: '插屏广告' },
  { value: 3, label: '原生广告' },
  { value: 4, label: '激励视频广告' },
  { value: 5, label: '横幅广告' },
]


const AdSlotManagement = () => {
  // 状态管理
  const [selectAdType, setSelectAdType] = useState<string | undefined>();
  const [selectedStatus, setSelectedStatus] = useState<true | false | undefined>();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(15);
  const [total, setTotal] = useState(15); // 模拟总数据量
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = currentPage * pageSize;
  const [dataSource, setDataSource] = useState<AdSlotModel[]>([]);
  const currentData = dataSource.slice(startIndex, endIndex);

  const navigate = useNavigate()
  const params = useParams();
  const [adFlowSelection, setAdFlowSelection] = useState<AdFlowSelect[]>()
  const [adMediaSelection, setAdMediaSelection] = useState<AdMediaSelect[]>()
  const [selectAdMedia, setSelectAdMedia] = useState<string | undefined>(params['media_id'])
  const [selectAdFlow, setSelectAdFlow] = useState<string | undefined>(params['flow_id'])
  const [refresh, setRefresh] = useState<{}>({})

  useEffect(() => {
    const selectAdFlowId = selectAdFlow ? selectAdFlow : adFlowSelection?.[0].value
    if (!selectAdFlowId){
      return
    }
    getMediaSelection4Flow(Number(selectAdFlowId)).then(value => {
      if (value.isSuccess&&value.data) {
        const adMediaSelection: AdMediaSelect[] = value.data.map(item => ({ label: item.name, value: String(item.id) }))
        setAdMediaSelection(adMediaSelection)
      }
    })
  }, [selectAdFlow]);

  useEffect(() => {
    querySlotList(currentPage,pageSize,selectAdMedia,selectAdType,selectedStatus).then(value => {
      if (!value.isSuccess) {
        if (value.code != 401) {
          alert('获取广告位失败')
        }
        return
      }
      const data = value.data
      if (!data) {
        alert('获取广告位失败')
        return
      }
      const list = data.list
      if (!list) {
        alert('获取广告位失败')
        return
      }

      if (data.media_selection_list){
        const result: AdMediaSelect[] = []
        data.media_selection_list.forEach((item: AdMediaSelection) => {
          result.push({ label: item.name, value: String(item.id) })
        })
        setAdMediaSelection(result)
        if (data.media_selection) {
          setSelectAdMedia(String(data.media_selection.id))
        }
      }
      if (data.flow_selection_list){
        const result: AdFlowSelect[] = []
        data.flow_selection_list.forEach((item: AdFlowSelection) => {
          result.push({ label: item.name, value: String(item.id) })
        })
        setAdFlowSelection(result)
        if (data.flow_selection) {
          setSelectAdFlow(String(data.flow_selection.id))
        }
      }
      setDataSource(list)
      setTotal(data.count)
    })
  }, [refresh, currentPage, pageSize])

  // 分页处理
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  const [showProps, setShowProps] = useState<ModifyAdSlotShowProps>({
    visible: false, // 控制弹窗显示
  })

  const handleAddAdSlot = () => {
    setShowProps({ visible: true })
  }

  const handleOnEditClick = (record: AdSlotModel) => {
    setShowProps({ visible: true, dataSource: record })
  }

  const handleEnableChange = (id: number, checked: boolean) => {
    toggleSlot(id, checked).then(value => {
      if (value.isSuccess) {
        setRefresh({})
      } else {
        if (value.code != 401) {
          if (checked) {
            alert('启动失败')
          } else {
            alert('关闭失败')
          }
        }
      }
    })
  }

  const handleDeleteClick = (record: AdSlotModel) => {
    if (confirm('确定要删除该广告位吗？')) {
      removeSlot(record.id,record.slot_id).then(value => {
        if (value.isSuccess) {
          setRefresh({})
        } else {
          if (value.code != 401) {
            alert('删除失败')
          }
        }
      })
    }
  }

  const handleToAdSlotClick = (record: AdSlotModel) => {
    navigate(`/flowManager/adCode?slot_id=${record.id}`)
  }

  function handleCreate(values: AdSlotModify, isEdit: boolean): void {
    setShowProps({ visible: false })
    var adFrequencySetting: AdFrequencySetting | undefined
    if (values.source_request_limit || values.source_show_limit || values.request_limit || values.show_limit || values.request_interval || values.virtual_request_limit) {
      adFrequencySetting = {
        source_request_limit: Number(values.source_request_limit),
        source_show_limit: Number(values.source_show_limit),
        request_limit: Number(values.request_limit),
        show_limit: Number(values.show_limit),
        request_interval: Number(values.request_interval),
        virtual_request_limit: Number(values.virtual_request_limit),
      }
    }
    if (!isEdit) {
      const createSlotBody: AdSlotCreateBody = {
        name: values.name,
        media_id: Number(values.media_id),
        ad_type: values.ad_type,
        request_timeout: values.request_timeout,
        open_frequency_setting: values.open_frequency_setting,
        ad_frequency_setting: adFrequencySetting,
      }
      addSlot(createSlotBody).then(value => {
        if (value.isSuccess) {
          setRefresh({})
        } else {
          if (value.code != 401) {
            alert('新增失败')
          }
        }
      })
    } else {
      const updateSlotBody: AdSlotUpdateBody = {
        id: Number(values.id),
        name: values.name,
        ad_type: values.ad_type,
        request_timeout: values.request_timeout,
        open_frequency_setting: values.open_frequency_setting,
        ad_frequency_setting: adFrequencySetting,
      }
      updateSlot(updateSlotBody).then(value => {
        if (value.isSuccess) {
          setRefresh({})
        } else {
          if (value.code != 401) {
            alert('编辑失败')
          }
        }
      })
    }
  }

  // 表格列定义
  const columns: ColumnsType<AdSlotModel> = [
    {
      title: '广告位',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space align="center">
          <div>
            <div>{text}</div>
            <div style={{ fontSize: 12, color: '#666' }}>{record.slot_id}</div>
          </div>
        </Space>
      ),
    },
    {
      title: '广告位类型',
      dataIndex: 'ad_type',
      key: 'ad_type',
      render: (ad_type) => {
        const text = AD_TYPE_OPTIONS?.find(item => item.value === ad_type)?.label || ad_type
        return <div style={{ maxWidth: 200, overflow: 'auto' }}>{text}</div>
      },
    },
    {
      title: '媒体',
      dataIndex: 'media_id',
      key: 'media_id',
      render: (flow_id: number) => {
        const text = adMediaSelection?.find(item => item.value === String(flow_id))?.label || flow_id
        return <div style={{ maxWidth: 200, overflow: 'auto' }}>{text}</div>
      },
    },
    {
      title: '是否启动',
      key: 'enable',
      dataIndex: 'enable',
      render: (status, record: AdSlotModel) => <Switch checked={status} onChange={(checked) => { handleEnableChange(record.id, checked) }} />, 
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <a onClick={() => { handleOnEditClick(record) }}>编辑</a>
          <a onClick={() => { handleToAdSlotClick(record) }}>流量分发</a>
          <a onClick={() => { handleDeleteClick(record) }} style={{ color: 'red' }}>删除</a>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '8px 24px 24px 24px', minHeight: '100vh' }}>
      {/* 顶部操作区 */}
      <div style={{
        background: '#fff',
        border: '1px solid #f0f0f0',
        borderRadius: 8,
        padding: 16,
        marginBottom: 16,
        overflow: 'hidden'
      }}>
        <Row justify="space-between" align="middle" gutter={24}>
          <Col>
            <span style={{
              marginRight: 24,
              fontWeight: '500',
              fontSize: '15px'
            }}>流量方: </span>
            <Select
                placeholder="请选择流量方"
                style={{ width: 240, marginRight: 24, }}
                value={selectAdFlow}
                onChange={(value) => setSelectAdFlow(value)}
                options={adFlowSelection}
            />
            <span style={{
              marginRight: 24,
              fontWeight: '500',
              fontSize: '15px'
            }}>媒体: </span>
            <Select
              placeholder="请选择媒体"
              style={{ width: 240, marginRight: 24, }}
              value={selectAdMedia}
              onChange={(value) => setSelectAdMedia(value)}
              options={adMediaSelection}
            />
            <span style={{
              marginRight: 24,
              fontWeight: '500',
              fontSize: '15px'
            }}>广告类型: </span>
            <Select
              placeholder="请选择广告类型"
              style={{ width: 240, marginRight: 24, }}
              value={selectAdType}
              onChange={(value) => setSelectAdType(value)}
              options={AD_TYPE_OPTIONS}
            />
            <span style={{
              marginRight: 24,
              fontWeight: '500',
              fontSize: '15px'
            }}>是否启动：</span>

            <Select
              placeholder="是否启用"
              style={{ width: 120 }}
              value={selectedStatus}
              onChange={(value) => setSelectedStatus(value)}
              options={[{ value: true, label: '启用' }, { value: false, label: '关闭' }]}
            />
          </Col>
          <Col>
            <Space size="middle">
              <Button onClick={() => {
                setCurrentPage(1)
                setPageSize(15)
                setSelectAdMedia(undefined)
                setSelectAdType(undefined)
                setSelectedStatus(undefined)
              }}>重置</Button>
              <Button type="primary" onClick={() => {
                setRefresh({})
              }}>搜索</Button>
            </Space>
          </Col>
        </Row>
      </div>

      {/* 表格区域 */}
      <div style={{
        overflow: 'auto', height: 'calc(100vh - 300px)', background: '#fff',
        border: '1px solid #f0f0f0',
        borderRadius: 8,
        padding: 16,
      }}>
        <Button type="primary" style={{ marginRight: 16 }} onClick={handleAddAdSlot}>
          添加
        </Button>
        <ModifyAdSlotModal
          show={showProps}
          onCancel={() => { setShowProps({ visible: false }) }}
          onConfirm={handleCreate}
          selectAdMedia={{ label: adMediaSelection?.find(item => item.value === selectAdMedia)?.label || '', value: selectAdMedia || '' }}
        />
        <Table
          columns={columns}
          dataSource={currentData}
          rowKey="id"
          pagination={false} // 使用自定义分页
        />
      </div>

      {/* 分页区 */}
      <Divider style={{ margin: '24px 0' }} />
      <Pagination
        current={currentPage}
        pageSize={pageSize}
        total={total}
        onChange={handlePageChange}
        onShowSizeChange={handlePageSizeChange}
        pageSizeOptions={['15', '30', '50']}
        showSizeChanger
        showQuickJumper
        showTotal={(total) => `共 ${total} 条`}
        style={{ float: 'right' }}
      />
    </div>
  );
};

export default AdSlotManagement;