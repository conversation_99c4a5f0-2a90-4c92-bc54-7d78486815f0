import React, {useEffect, useRef, useState} from 'react';
import {<PERSON><PERSON>, Col, Divider, Pagination, Row, Select, Space, Switch, Table,} from 'antd';
import {ColumnsType} from 'antd/es/table';
import {useParams} from 'react-router-dom';
import ModifyAdCodeModal, {ModifyAdCodeShowProps} from './ModifyAdCodeModal';
import {AdMediaSelect} from "@/pages/media-management/ad-slot/AdSlotManagement";
import {
    AdCodeCreateBody,
    AdCodeResp,
    AdCodeUpdateBody,
    addCode,
    queryCodeList,
    removeCode,
    toggleCode
} from "@/data/flow_manager/code";
import {AdSlotSelection, getAdSlotSelection4Media, getAdSourceSelection4Slot} from "@/data/flow_manager/slot";
import {AdFlowSelect} from "@/pages/media-management/media/MediaManagement";
import {AdMediaSelection, getMediaSelection4Flow} from "@/data/flow_manager/media";
import {updateSource} from "@/data/budget_manager/source";
import {AdFlowSelection} from "@/data/flow_manager/flow";

export type AdSlotSelect = {
    label: string;
    value: string;
}

export type AdSourceSelect = {
    label: string;
    value: number;
}

const CodeSlotManagement = () => {
    const [selectedStatus, setSelectedStatus] = useState<true | false | undefined>();
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(15);
    const [total, setTotal] = useState(15); // 模拟总数据量
    const params = useParams();
    const [adFlowSelection, setAdFlowSelection] = useState<AdFlowSelect[]>()
    const [adSlotSelect, setAdSlotSelect] = useState<AdSlotSelect[]>()
    const [adMediaSelection, setAdMediaSelection] = useState<AdMediaSelect[]>()
    const adSlotSelectionList = useRef<AdSlotSelection[]>([])
    const [selectAdFlow, setSelectAdFlow] = useState<string | undefined>(params['flow_id'])
    const [selectAdMedia, setSelectAdMedia] = useState<string | undefined>(params['media_id'])
    const [selectAdSlot, setSelectAdSlot] = useState<string | undefined>(params['slot_id'])

    const adSourceSelectList = useRef<AdSourceSelect[]>([])

    const [refresh, setRefresh] = useState<{}>({})

    // 当前显示的数据（模拟分页）
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = currentPage * pageSize;
    const [dataSource, setDataSource] = useState<AdCodeResp[]>([]);
    const currentData = dataSource.slice(startIndex, endIndex);

    useEffect(() => {
        const selectAdSlotId = selectAdSlot ? selectAdSlot : adSlotSelect?.[0].value
        if (!selectAdSlotId) {
            return
        }
        getAdSourceSelection4Slot(selectAdSlotId).then(value => {
            if (value.isSuccess && value.data) {
                adSourceSelectList.current = value.data.map(item => ({label: item.name, value: item.id}))
            }
        })
    }, [selectAdSlot]);


    useEffect(() => {
        const selectAdMediaId = selectAdMedia ? selectAdMedia : adMediaSelection?.[0].value
        if (!selectAdMediaId) {
            return
        }
        getAdSlotSelection4Media(selectAdMediaId).then(value => {
            console.log(JSON.stringify(value))
            if (value.isSuccess && value.data) {
                adSlotSelectionList.current = value.data
                const adSlotSelection: AdSlotSelect[] = value.data.map(item => ({
                    label: item.name,
                    value: String(item.slot_code)
                }))
                setAdSlotSelect(adSlotSelection)
                setSelectAdSlot(adSlotSelection[0].value)
            }
        })
    }, [selectAdMedia])

    useEffect(() => {
        const selectAdFlowId = selectAdFlow ? selectAdFlow : adFlowSelection?.[0].value
        if (!selectAdFlowId) {
            return
        }
        getMediaSelection4Flow(Number(selectAdFlowId)).then(value => {
            if (value.isSuccess && value.data) {
                const adMediaSelection: AdMediaSelect[] = value.data.map(item => ({
                    label: item.name,
                    value: String(item.id)
                }))
                setAdMediaSelection(adMediaSelection)
                setSelectAdMedia(adMediaSelection[0].value)
            }
        })
    }, [selectAdFlow]);

    useEffect(() => {
        queryCodeList(currentPage, pageSize, Number(selectAdSlot), selectedStatus).then(value => {
            if (!value.isSuccess) {
                if (value.code != 401) {
                    alert('获取广告位失败')
                }
                return
            }
            const data = value.data
            if (!data) {
                alert('获取广告位失败')
                return
            }
            const list = data.list
            if (!list) {
                alert('获取广告位失败')
                return
            }
            setDataSource(list)
            setTotal(data.count)

            if (data.slot_selection_list) {
                const result: AdSlotSelect[] = []
                data.slot_selection_list.forEach((item: AdSlotSelection) => {
                    result.push({label: item.name, value: String(item.slot_code)})
                })
                setAdSlotSelect(result)
                if (data.slot_selection) {
                    setSelectAdSlot(String(data.slot_selection.slot_code))
                }
            }
            if (data.media_selection_list) {
                const result: AdMediaSelect[] = []
                data.media_selection_list.forEach((item: AdMediaSelection) => {
                    result.push({label: item.name, value: String(item.id)})
                })
                setAdMediaSelection(result)
                if (data.media_selection) {
                    setSelectAdMedia(String(data.media_selection.id))
                }
            }
            if (data.flow_selection_list) {
                const result: AdFlowSelect[] = []
                data.flow_selection_list.forEach((item: AdFlowSelection) => {
                    result.push({label: item.name, value: String(item.id)})
                })
                setAdFlowSelection(result)
                if (data.flow_selection) {
                    setSelectAdFlow(String(data.flow_selection.id))
                }
            }
        })
    }, [refresh, currentPage, pageSize])

    // 分页处理
    const handlePageChange = (page: number) => {
        setCurrentPage(page);
    };

    const handlePageSizeChange = (size: number) => {
        setPageSize(size);
        setCurrentPage(1);
    };

    const [showProps, setShowProps] = useState<ModifyAdCodeShowProps>({
        visible: false, // 控制弹窗显示
    })

    function handleAddAdSlot(): void {
        setShowProps({visible: true})
    }

    const handleOnEditClick = (record: AdCodeResp) => {
        setShowProps({visible: true, dataSource: record})
    }

    const handleEnableChange = (slotId: number, id: number, checked: boolean) => {
        toggleCode(slotId, id, checked).then(value => {
            if (value.isSuccess) {
                setRefresh({})
            } else {
                if (value.code != 401) {
                    if (checked) {
                        alert('启动失败')
                    } else {
                        alert('关闭失败')
                    }
                }
            }
        })
    }

    const handleDeleteClick = (record: AdCodeResp) => {
        if (confirm('确定要删除该流量配置吗？')) {
            removeCode(record.ad_slot_id, record.id).then(value => {
                if (value.isSuccess) {
                    setRefresh({})
                } else {
                    if (value.code != 401) {
                        alert('删除失败')
                    }
                }
            })
        }
    }

    function handleCreate(values: AdCodeCreateBody | AdCodeUpdateBody, isEdit: boolean): void {
        setShowProps({visible: false})
        if (!isEdit) {
            addCode(values).then(value => {
                if (value.isSuccess) {
                    setRefresh({})
                } else {
                    if (value.code != 401) {
                        alert('新增失败')
                    }
                }
            })
        } else {
            updateSource(values).then(value => {
                if (value.isSuccess) {
                    setRefresh({})
                } else {
                    if (value.code != 401) {
                        alert('编辑失败')
                    }
                }
            })
        }
    }

    // 表格列定义
    const columns: ColumnsType<AdCodeResp> = [
        {
            title: '广告位',
            dataIndex: 'ad_slot_name',
            key: 'ad_slot_name',
            render: (text, record) => (
                <Space align="center">
                    <div>
                        <div>{text}</div>
                        <div style={{fontSize: 12, color: '#666'}}>{record.ad_slot_id}</div>
                    </div>
                </Space>
            ),
        },
        {
            title: '广告源',
            dataIndex: 'ad_source_name',
            key: 'ad_source_name',
            render: (text, record) => (
                <Space align="center">
                    <div>
                        <div>{text}</div>
                        <div style={{fontSize: 12, color: '#666'}}>{record.ad_source_id}</div>
                    </div>
                </Space>
            ),
        },
        {
            title: '出价系数',
            dataIndex: 'commission_rate',
            key: 'commission_rate',
            render: (text) => <div style={{maxWidth: 200, overflow: 'auto'}}>{text}</div>,
        },

        {
            title: '底价',
            dataIndex: 'reserve_price',
            key: 'reserve_price',
            render: (text) => <div style={{maxWidth: 200, overflow: 'auto'}}>{text}</div>,
        },
        {
            title: '是否启动',
            key: 'is_enable',
            dataIndex: 'is_enable',
            render: (status, record: AdCodeResp) => <Switch checked={status} onChange={(checked) => {
                handleEnableChange(record.ad_slot_id, record.id, checked)
            }}/>,
        },
        {
            title: '操作',
            key: 'action',
            render: (_, record) => (
                <Space size="small">
                    <a onClick={() => {
                        handleOnEditClick(record)
                    }}>编辑</a>
                    <a onClick={() => {
                        handleDeleteClick(record)
                    }} style={{color: 'red'}}>删除</a>
                </Space>
            ),
        },
    ];

    return (
        <div style={{padding: '8px 24px 24px 24px', minHeight: '100vh'}}>
            {/* 顶部操作区 */}
            <div style={{
                background: '#fff',
                border: '1px solid #f0f0f0',
                borderRadius: 8,
                padding: 16,
                marginBottom: 16,
                overflow: 'hidden'
            }}>
                <Row justify="space-between" align="middle" gutter={24}>
                    <Col>
            <span style={{
                marginRight: 24,
                fontWeight: '500',
                fontSize: '15px'
            }}>媒体: </span>
                        <Select
                            placeholder="请选择媒体"
                            style={{width: 240, marginRight: 24,}}
                            value={selectAdMedia}
                            onChange={(value) => setSelectAdMedia(value)}
                            options={adMediaSelection}
                        />
                        <span style={{
                            marginRight: 24,
                            fontWeight: '500',
                            fontSize: '15px'
                        }}>广告位: </span>
                        <Select
                            placeholder="请选择广告位"
                            style={{width: 240, marginRight: 24,}}
                            value={selectAdSlot}
                            onChange={(value) => setSelectAdSlot(value)}
                            options={adSlotSelect}
                        />
                        <span style={{
                            marginRight: 24,
                            fontWeight: '500',
                            fontSize: '15px'
                        }}>是否启动：</span>
                        <Select
                            placeholder="是否启用"
                            style={{width: 120}}
                            value={selectedStatus}
                            onChange={(value) => setSelectedStatus(value)}
                            options={[{value: true, label: '启用'}, {value: false, label: '关闭'}]}
                        />
                    </Col>
                    <Col>
                        <Space size="middle">
                            <Button onClick={() => {
                                setCurrentPage(1)
                                setPageSize(15)
                                setSelectAdMedia(adMediaSelection?.[0].value)
                                setSelectAdSlot(adSlotSelect?.[0].value)
                                setSelectedStatus(undefined)
                            }}>重置</Button>
                            <Button type="primary" onClick={() => {
                                setRefresh({})
                            }}>搜索</Button>
                        </Space>
                    </Col>
                </Row>
            </div>

            {/* 表格区域 */}
            <div style={{
                overflow: 'auto', height: 'calc(100vh - 300px)', background: '#fff',
                border: '1px solid #f0f0f0',
                borderRadius: 8,
                padding: 16,
            }}>
                <Button type="primary" style={{marginRight: 16}} onClick={handleAddAdSlot}>
                    添加
                </Button>
                <ModifyAdCodeModal
                    show={showProps}
                    onCancel={() => {
                        setShowProps({visible: false})
                    }}
                    onConfirm={handleCreate}
                    selectAdSlot={{
                        label: adSlotSelect?.find(item => item.value === selectAdSlot)?.label || '',
                        value: selectAdSlot || ''
                    }}
                    adSourceSelectList={adSourceSelectList.current}
                />
                <Table
                    columns={columns}
                    dataSource={currentData}
                    rowKey="id"
                    pagination={false} // 使用自定义分页
                />
            </div>

            {/* 分页区 */}
            <Divider style={{margin: '24px 0'}}/>
            <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={total}
                onChange={handlePageChange}
                onShowSizeChange={handlePageSizeChange}
                pageSizeOptions={['15', '30', '50']}
                showSizeChanger
                showQuickJumper
                showTotal={(total) => `共 ${total} 条`}
                style={{float: 'right'}}
            />
        </div>
    );
};

export default CodeSlotManagement;