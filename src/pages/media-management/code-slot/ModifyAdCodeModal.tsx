import { Modal, Form, Button, message, InputNumber, Select, Typography } from 'antd';
import { useEffect } from 'react';
import { AdSlotSelect, AdSourceSelect } from './CodeSlotManagement';
import {AdCodeCreateBody, AdCodeResp, AdCodeUpdateBody} from "@/data/flow_manager/code";

export type ModifyAdCodeShowProps = {
    visible: boolean;
    dataSource?: AdCodeResp;
};

export type ModifyAdCodeModalProps = {
    show: ModifyAdCodeShowProps;
    selectAdSlot: AdSlotSelect;
    adSourceSelectList: AdSourceSelect[]
    onConfirm: (values: AdCodeCreateBody | AdCodeUpdateBody, isEdit: boolean) => void;
    onCancel: () => void;
};

const ModifyAdCodeModal = ({ show, selectAdSlot, adSourceSelectList, onConfirm, onCancel }: ModifyAdCodeModalProps) => {
    const [form] = Form.useForm();
    const { visible, dataSource } = show;
    const isEdit = !!dataSource?.id;

    useEffect(() => {
        if (visible) {
            form.resetFields();
            dataSource && form.setFieldsValue({
                ...dataSource,
                ad_source_id: dataSource.ad_source_id?.toString(),
            });
        }
    }, [visible]);

    const handleSubmit = () => {
        form.validateFields()
            .then(values => {
                const formattedValues = {
                    ...values,
                    ad_slot_id:Number(selectAdSlot.value),
                    commission_rate: Number(values.commission_rate),
                    reserve_price: Number(values.reserve_price),
                };
                onConfirm(isEdit ? { ...formattedValues, id: dataSource?.id } : formattedValues, isEdit);
            })
            .catch(err => message.error('表单验证失败'));
    };

    const readonlyStyle = {
        backgroundColor: '#f5f5f5',
        padding: '4px 8px',
        borderRadius: 4,
        color: '#666',
        cursor: 'not-allowed',
        display: 'inline-block',
        width: '100%' // 保持和Select相同宽度
    };

    return (
        <Modal
            title={isEdit ? '编辑广告代码' : '新建广告代码'}
            open={visible}
            onCancel={onCancel}
            footer={null}
            width={600}
            destroyOnClose
        >
            <Form form={form} layout="vertical">
                <Form.Item
                    label="广告位名称"
                    name="ad_slot_id"
                    initialValue={selectAdSlot.value}
                    rules={[{ required: true }]}
                >
                    <Typography.Text code style={readonlyStyle}>
                        {selectAdSlot.label}
                    </Typography.Text>
                </Form.Item>

                <Form.Item
                    label="广告源"
                    name="ad_source_id"
                    rules={[{ required: true, message: '请选择广告源' }]}
                >
                    <Select options={adSourceSelectList} />
                </Form.Item>

                <Form.Item
                    label="佣金比例(%)"
                    name="commission_rate"
                    rules={[
                        { required: true, message: '请输入佣金比例' },
                        { type: 'number', min: 0, max: 100, message: '请输入0-100之间的数字' }
                    ]}
                >
                    <InputNumber style={{ width: '100%' }} precision={2} />
                </Form.Item>

                <Form.Item
                    label="底价(分)"
                    name="reserve_price"
                    rules={[
                        { required: false, message: '请输入底价' },
                        { type: 'number', min: 0, message: '不能小于0' }
                    ]}
                >
                    <InputNumber style={{ width: '100%' }} precision={2} />
                </Form.Item>

                <div style={{ textAlign: 'right', marginTop: 24 }}>
                    <Button onClick={onCancel} style={{ marginRight: 8 }}>取消</Button>
                    <Button type="primary" onClick={handleSubmit}>确定</Button>
                </div>
            </Form>
        </Modal>
    );
};

export default ModifyAdCodeModal;