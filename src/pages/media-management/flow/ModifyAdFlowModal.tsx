import { Modal, Form, Input, Select, Button, message, Typography } from 'antd';
import { useEffect } from 'react';
import {AdFlow, AdFlowModify} from "@/data/flow_manager/flow";


export  type ModifyBudgetShowProps = {
    visible: boolean; // 控制弹窗显示
    dataSource?: AdFlow
}

type ModifyBudgetModalProps = {
    show: ModifyBudgetShowProps; // 控制弹窗显示
    onConfirm: (values: AdFlowModify, isEdit: boolean, dataSource?: AdFlow) => void; // 创建预算方的回调函数
    onCancel: () => void;
}


const ModifyBudgetModal = ({ show, onConfirm, onCancel }: ModifyBudgetModalProps) => {
    const [form] = Form.useForm<AdFlowModify>();
    const {visible,dataSource} = show;
    const isEdit = dataSource != undefined

    useEffect(() => {
        console.log('show',show);
        const {visible,dataSource} = show;
        if (visible&&dataSource) {
            form.resetFields();
            form.setFieldsValue({ ...dataSource })
        } else if(visible){
            console.log('重置表单');
            form.resetFields();
        }
    }, [show])

    // 表单提交处理
    const handleSubmit = () => {
        form.validateFields()
            .then(values => {
                onConfirm(values, isEdit, dataSource);
            })
            .catch(err => console.log('表单验证错误', err));
    };

    return (
        <Modal
            destroyOnClose={true}
            title={isEdit ? '编辑流量方' : '创建流量方'}
            open={visible}
            onCancel={onCancel} // 取消创建的回调函
            footer={null} // 自定义底部按钮
            width={520}
        >
            <Form
                form={form}
                layout="vertical"
                style={{ padding: 24 }}
                initialValues={dataSource}
            >
               <Form.Item
                    label="流量方名称"
                    name="name"
                    rules={[{
                        required: true,
                        message: '请输入流量方名称',
                        min: 2,
                        max: 50,
                        pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_-]{2,50}$/,
                        whitespace: true,
                    }]}
                >
                    <Input placeholder="请输入2-50位字符" />
                </Form.Item>

                <Form.Item
                    label="* 企业名称"
                    name="company_name"
                    rules={[{
                        required: true,
                        message: '请输入企业名称',
                        min: 2,
                        max: 50,
                        pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_-]{2,50}$/,
                        whitespace: true,
                    }]}
                >
                    <Input placeholder="请输入2-50位字符" />
                </Form.Item>

                <Form.Item
                    label="* 联系人"
                    name="contact"
                    rules={[{
                        required: true,
                        message: '请输入联系人',
                        min: 2,
                        max: 50,
                        pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_-]{2,50}$/,
                    }]}
                >
                    <Input placeholder="请输入2-50位字符" />
                </Form.Item>

                <Form.Item
                    label="* 联系电话"
                    name="phone"
                    rules={[{
                        required: true,
                        message: '请输入正确的手机号',
                        pattern: /^1[3-9]\d{9}$/,
                    }]}
                >
                    <Input placeholder="请输入手机号" />
                </Form.Item>

                <div style={{ textAlign: 'right', padding: '16px 24px 24px' }}>
                    <Button onClick={onCancel} style={{ marginRight: 8 }}>
                        取消
                    </Button>
                    <Button type="primary" onClick={handleSubmit}>
                        确定
                    </Button>
                </div>
            </Form>
        </Modal>
    );
};

export default ModifyBudgetModal;