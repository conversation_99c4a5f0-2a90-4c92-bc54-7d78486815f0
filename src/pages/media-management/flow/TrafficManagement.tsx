import { useEffect, useState } from 'react';
import {
  Table,
  Pagination,
  Button,
  Select,
  Switch,
  Row,
  Col,
  Divider,
  Space,
  message,

} from 'antd';
import ModifyBudgetModal, {ModifyBudgetShowProps } from './ModifyAdFlowModal';
import { ColumnsType } from 'antd/es/table';
import { useNavigate } from 'react-router-dom';
import {
  addFlow,
  AdFlow,
  AdFlowModify,
  queryFlowList,
  removeFlow,
  toggleFlow,
  updateFlow
} from "@/data/flow_manager/flow";



const TrafficManagement = () => {
  const [selectedStatus, setSelectedStatus] = useState<true | false | undefined>();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(15);
  const [total, setTotal] = useState(100); // 模拟总数据量
  const [dataSource, setDataSource] = useState<AdFlow[]>([]);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = currentPage * pageSize;
  const currentData = dataSource.slice(startIndex, endIndex);
  const [refresh, setRefresh] = useState<any>({})
  const navigate =useNavigate()

  useEffect(() => {
    queryFlowList(currentPage, pageSize).then(value => {
      console.log(JSON.stringify(value))
      if (!value.isSuccess) {
        if (value.code != 401) {
          alert('获取流量方失败')
        }
        return
      }
      const data = value.data
      if (!data) {
        alert('获取流量方失败')
        return
      }
      const list = data.list
      if (!list) {
        alert('获取流量方失败')
        return
      }
      setDataSource(list)
      setTotal(data.count)
    })
  }, [refresh])

  const [showProps, setShowProps] = useState<ModifyBudgetShowProps>({
    visible: false, // 控制弹窗显示
  })
  const handleAddAdFlow = () => {
    setShowProps({ visible: true })
  };
  const handleCreate = (values: AdFlowModify, isEdit: boolean, dataSource?: AdFlow) => {
    setShowProps({ visible: false })
    if (isEdit) {
      // 编辑
      console.log('编辑预算方');
      const updateFlowData = {
        id: dataSource?.id,
        company_name: values.company_name,
        contact: values.contact,
        phone: values.phone,
        enable:values.enable,
      }
      updateFlow(updateFlowData).then(values => {
        if (values.isSuccess) {
          setRefresh({})
        } else {
          if (values.code != 401) {
            alert('编辑失败')
          }
        }
      })
    } else {
      // 新增
      const createFlow = {
        name: values.name,
        company_name: values.company_name,
        contact: values.contact,
        phone: values.phone,
        enable: true,
      }
      addFlow(createFlow).then(values => {
        if (values.isSuccess) {
          setRefresh({})
        } else {
          if (values.code != 401) {
            alert('新增失败')
          }
        }
      })
    }
  };

  const handleOnEditClick = (record: AdFlow) => {
    setShowProps({ visible: true, dataSource: record })
  }

  const handleToAdMediaClick = (record: AdFlow) => {
    navigate(`/flowManager/media?flow_id=${record.id}`) // 跳转到媒体管理页面，传递 flow_id 参
  }

  const handleDeleteClick = (record: AdFlow) => {
    if (confirm('确定要删除该流量方吗？')) {
      removeFlow(record.id).then(value => {
        if (value.isSuccess) {
          setRefresh({})
        } else {
          if (value.code != 401) {
            alert('删除失败')
          }
        }
      })
    }
  }

  // 表格列定义
  const columns: ColumnsType<AdFlow> = [
    {
      title: '流量方名称',
      dataIndex: 'name',
      key: 'name',
      render: (text) => <div style={{ maxWidth: 200, overflow: 'auto' }}>{text}</div>,
    },
    {
      title: '公司名称',
      dataIndex: 'company_name',
      key: 'company_name',
      render: (text) => <div style={{ maxWidth: 200, overflow: 'auto' }}>{text}</div>,
    },
    {
      title: '联系人',
      dataIndex: 'contact',
      key: 'contact',
      render: (text) => <div style={{ maxWidth: 200, overflow: 'auto' }}>{text}</div>,
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      key: 'phone',
      render: (text) => <div style={{ maxWidth: 200, overflow: 'auto' }}>{text}</div>,
    },
    {
      title: '是否启动',
      key: 'enable',
      dataIndex: 'enable',
      render: (status, record: AdFlow) => <Switch checked={status} onChange={(checked) => { handleEnableChange(record.id, checked) }} />, // 模拟不可编辑状态
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: AdFlow) => (
        <Space size="small">
          <a onClick={() => { handleOnEditClick(record) }}>编辑</a>
          <a onClick={() => { handleToAdMediaClick(record) }}>管理媒体</a>
          <a onClick={() => { handleDeleteClick(record) }} style={{ color: 'red' }}>删除</a>
        </Space>
      ),
    },
  ];

  // 分页处理
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  const handleEnableChange = (id: number, checked: boolean) => {
    toggleFlow(id, checked).then(value => {
      if (value.isSuccess) {
        setRefresh({})
      } else {
        if (value.code != 401) {
          if (checked) {
            alert('启动失败')
          } else {
            alert('关闭失败')
          }
        }
      }
    })
  }

  return (
    <div style={{ padding: '8px 24px 24px 24px', minHeight: '100vh' }}>
      {/* 顶部操作区 */}
      <div style={{
        background: '#fff',
        border: '1px solid #f0f0f0',
        borderRadius: 8,
        padding: 16,
        marginBottom: 16,
        overflow: 'hidden'
      }}>
        <Row justify="space-between" align="middle" gutter={24}>
          <Col>
            <span style={{
              marginRight: 24,
              fontWeight: '500',
              fontSize: '15px'
            }}>是否启动：</span>

            <Select
              placeholder="是否启用"
              style={{ width: 120 }}
              value={selectedStatus}
              onChange={(value) => setSelectedStatus(value)}
              options={[{ value: true, label: '启用' }, { value: false, label: '关闭' }]}
            />
          </Col>
          <Col>
            <Space size="middle">
              <Button onClick={() => {/* 待实现重置逻辑 */ }}>重置</Button>
              <Button type="primary" onClick={() => {/* 待实现搜索逻辑 */ }}>搜索</Button>
            </Space>
          </Col>
        </Row>
      </div>



      {/* 表格区域 */}
      <div style={{
        overflow: 'auto', height: 'calc(100vh - 300px)', background: '#fff',
        border: '1px solid #f0f0f0',
        borderRadius: 8,
        padding: 16,
      }}>
        <Button type="primary" style={{ marginRight: 16, marginBottom: 16 }} onClick={handleAddAdFlow}>
          添加
        </Button>
        <ModifyBudgetModal
            show={showProps}
            onCancel={() => {
              setShowProps({visible:false})
            }}
            onConfirm={handleCreate}
          />
        <Table
          columns={columns}
          dataSource={currentData}
          rowKey="id"
          pagination={false} // 使用自定义分页
          scroll={{ y: 'calc(100vh - 400px)' }} // 表格内容滚动高度
        />
      </div>

      {/* 分页区 */}
      <Divider style={{ margin: '24px 0' }} />
      <Pagination
        current={currentPage}
        pageSize={pageSize}
        total={total}
        onChange={handlePageChange}
        onShowSizeChange={handlePageSizeChange}
        pageSizeOptions={['15', '30', '50']}
        showSizeChanger
        showQuickJumper
        showTotal={(total) => `共 ${total} 条`}
        style={{ float: 'right' }}
      />
    </div>
  );
};

export default TrafficManagement;
