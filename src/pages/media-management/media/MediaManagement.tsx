import React, { useEffect, useState } from 'react';
import {
  Table,
  Pagination,
  Button,
  Input,
  Select,
  Switch,
  Space,
  Row,
  Col,
  Divider,

} from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useNavigate, useParams } from 'react-router-dom';
import ModifyMediaModal, { ModifyMediaShowProps } from './ModifyMediaModal';
import {
  addMedia,
  AdMediaCreateBody,
  AdMediaModel,
  AdMediaUpdateBody, queryMediaList, removeMedia, toggleMedia,
  updateMedia
} from "@/data/flow_manager/media";
import {AdFlowSelection} from "@/data/flow_manager/flow";

export type AdFlowSelect = {
  label: string;
  value: string;
}

const MediaManagement = () => {
  const [selectedPlatform, setSelectedPlatform] = useState<
    'Android' | 'iOS' | undefined
  >();
  const [selectedStatus, setSelectedStatus] = useState<true | false | undefined>();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(15);
  const [total, setTotal] = useState(100); // 模拟总数据量

  // 当前显示的数据（模拟分页）
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = currentPage * pageSize;
  const [dataSource, setDataSource] = useState<AdMediaModel[]>([]);
  const currentData = dataSource.slice(startIndex, endIndex);

  const params = useParams();
  console.log(params);
  const [adFlowSelection, setAdFlowSelection] = useState<AdFlowSelect[]>()
  const [selectAdFlow, setSelectAdFlow] = useState<string | undefined>(params['flow_id'])
  const [refresh, setRefresh] = useState<{}>({})
  const navigate = useNavigate()

  useEffect(() => {
    queryMediaList(currentPage, pageSize, Number(selectAdFlow), selectedPlatform ? selectedPlatform === 'Android' ? 1 : 2 : undefined, selectedStatus).then(value => {
      if (!value.isSuccess) {
        if (value.code != 401) {
          alert('获取媒体失败')
        }
        return
      }
      const data = value.data
      if (!data) {
        alert('获取媒体失败')
        return
        }
      const list = data.list
      if (!list) {
        alert('获取媒体失败')
        return
      }
      if (data.flow_selection_list){
        const result: AdFlowSelect[] = []
        data.flow_selection_list.forEach((item: AdFlowSelection) => {
          result.push({ label: item.name, value: String(item.id) })
        })
        setAdFlowSelection(result)
        if (data.history_flow){
          setSelectAdFlow(String(data.history_flow.id))
        }
      }
      setDataSource(list)
      setTotal(data.count)
    })
  }, [refresh])

  const [showProps, setShowProps] = useState<ModifyMediaShowProps>({
    visible: false, // 控制弹窗显示
  })

  const handleOnEditClick = (record: AdMediaModel) => {
    setShowProps({ visible: true, dataSource: record })
  }

  const handleCreate = (values: AdMediaCreateBody | AdMediaUpdateBody, isEdit: boolean) => {
    setShowProps({ visible: false })
    if (isEdit) {
      updateMedia(values).then(value => {
        if (value.isSuccess) {
          setRefresh({})
        } else {
          if (value.code != 401) {
            alert('编辑失败')
          }
        }
      })
    } else {
      addMedia(values).then(value => {
        if (value.isSuccess) {
          setRefresh({})
        } else {
          if (value.code != 401) {
            alert('新增失败')
          }
        }
      })
    }
  }

  const handleEnableChange = (id: number, checked: boolean) => {
    toggleMedia(id, checked).then(value => {
      if (value.isSuccess) {
        setRefresh({})
      } else {
        if (value.code != 401) {
          if (checked) {
            alert('启动失败')
          } else {
            alert('关闭失败')
          }
        }
      }
    })
  }

  const handleDeleteClick = (record: AdMediaModel) => {
    if (confirm('确定要删除该流量方吗？')) {
      removeMedia(record.id).then(value => {
        if (value.isSuccess) {
          setRefresh({})
        } else {
          if (value.code != 401) {
            alert('删除失败')
          }
        }
      })
    }
  }

  const handleToAdSlotClick = (record: AdMediaModel) => {
    navigate(`/flowManager/adSlot?media_id=${record.id}`)
  }

  // 分页处理
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  function handleAddAdMedia(): void {
    setShowProps({ visible: true })
  }

  // 表格列定义
  const columns: ColumnsType<AdMediaModel> = [
    {
      title: '媒体',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space align="center">
          <div>
            <div>{text}</div>
            <div style={{ fontSize: 12, color: '#666' }}>{record.id}</div>
          </div>
        </Space>
      ),
    },
    {
      title: '流量方',
      dataIndex: 'flow_id',
      key: 'flow_id',
      render: (flow_id: number) => {
        const text = adFlowSelection?.find(item => item.value === String(flow_id))?.label || flow_id
        return <div style={{ maxWidth: 200, overflow: 'auto' }}>{text}</div>
      },
    },
    {
      title: '平台类型',
      dataIndex: 'os_type',
      key: 'os_type',
      render: (os_type: number) => {
        const text = os_type === 1 ? 'Android' : os_type === 2 ? 'iOS' : '未知'
        return <div style={{ maxWidth: 200, overflow: 'auto' }}>{text}</div>
      },
    },
    {
      title: '包名',
      dataIndex: 'package_name',
      key: 'package_name',
      render: (text) => <div style={{ maxWidth: 200, overflow: 'auto' }}>{text}</div>,
    },
    {
      title: '是否启动',
      key: 'enable',
      dataIndex: 'enable',
      render: (status, record: AdMediaModel) => <Switch checked={status} onChange={(checked) => { handleEnableChange(record.id, checked) }} />, // 模拟不可编辑状态
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <a onClick={() => { handleOnEditClick(record) }}>编辑</a>
          <a onClick={() => { handleToAdSlotClick(record) }}>管理广告位</a>
          <a onClick={() => { handleDeleteClick(record) }} style={{ color: 'red' }}>删除</a>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '8px 24px 24px 24px', minHeight: '100vh' }}>
      {/* 顶部操作区 */}
      <div style={{
        background: '#fff',
        border: '1px solid #f0f0f0',
        borderRadius: 8,
        padding: 16,
        marginBottom: 16,
        overflow: 'hidden'
      }}>
        <Row justify="space-between" align="middle" gutter={24}>
          <Col>
            <span style={{
              marginRight: 24,
              fontWeight: '500',
              fontSize: '15px'
            }}>流量方: </span>
            <Select
              placeholder="请选择流量方"
              style={{ width: 240, marginRight: 24, }}
              value={selectAdFlow}
              onChange={(value) => setSelectAdFlow(value)}
              options={adFlowSelection}
            />
            <span style={{
              marginRight: 24,
              fontWeight: '500',
              fontSize: '15px'
            }}>平台类型: </span>
            <Select
              placeholder="请选择平台类型"
              style={{ width: 240, marginRight: 24, }}
              value={selectedPlatform}
              onChange={(value) => setSelectedPlatform(value)}
              options={[
                { value: 'Android', label: 'Android' },
                { value: 'iOS', label: 'iOS' },
              ]}
            />
            <span style={{
              marginRight: 24,
              fontWeight: '500',
              fontSize: '15px'
            }}>是否启动：</span>

            <Select
              placeholder="是否启用"
              style={{ width: 120 }}
              value={selectedStatus}
              onChange={(value) => setSelectedStatus(value)}
              options={[{ value: true, label: '启用' }, { value: false, label: '关闭' }]}
            />
          </Col>
          <Col>
            <Space size="middle">
              <Button onClick={() => {
                setSelectedPlatform(undefined)
                setSelectedStatus(undefined)
                setSelectAdFlow(undefined)
              }}>重置</Button>
              <Button type="primary" onClick={() => {
                setRefresh({})
              }}>搜索</Button>
            </Space>
          </Col>
        </Row>
      </div>

      {/* 表格区域 */}
      <div style={{
        overflow: 'auto', height: 'calc(100vh - 300px)', background: '#fff',
        border: '1px solid #f0f0f0',
        borderRadius: 8,
        padding: 16,
      }}>
        <Button type="primary" style={{ marginRight: 16 }} onClick={handleAddAdMedia}>
          添加
        </Button>
        <ModifyMediaModal
          show={showProps}
          selectAdFlow={{ label: adFlowSelection?.find(item => item.value === selectAdFlow)?.label || '', value: selectAdFlow || '' }}
          onConfirm={handleCreate} // 传递 handleCreate 函数作为 onConfirm 回调
          onCancel={() => {
            setShowProps({ visible: false })
          }}
        />
        <Table
          columns={columns}
          dataSource={currentData}
          rowKey="id"
          pagination={false} // 使用自定义分页
        />
      </div>

      {/* 分页区 */}
      <Divider style={{ margin: '24px 0' }} />
      <Pagination
        current={currentPage}
        pageSize={pageSize}
        total={total}
        onChange={handlePageChange}
        onShowSizeChange={handlePageSizeChange}
        pageSizeOptions={['15', '30', '50']}
        showSizeChanger
        showQuickJumper
        showTotal={(total) => `共 ${total} 条`}
        style={{ float: 'right' }}
      />
    </div>
  );
};

export default MediaManagement;