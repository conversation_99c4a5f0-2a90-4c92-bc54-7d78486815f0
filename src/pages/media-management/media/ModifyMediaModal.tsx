import { Modal, Form, Input, Select, Button, message, Typography } from 'antd';
import { useEffect } from 'react';
import { AdFlowSelect } from './MediaManagement';
import {AdMediaCreateBody, AdMediaModel, AdMediaUpdateBody} from "@/data/flow_manager/media";

export type ModifyMediaShowProps = {
  visible: boolean;
  dataSource?: AdMediaModel;
};

export type ModifyMediaModalProps = {
  show: ModifyMediaShowProps;
  selectAdFlow: AdFlowSelect
  onConfirm: (values: AdMediaCreateBody | AdMediaUpdateBody, isEdit: boolean) => void;
  onCancel: () => void;
};

const ModifyMediaModal = ({ show, selectAdFlow, onConfirm, onCancel }: ModifyMediaModalProps) => {
  const [form] = Form.useForm();
  const { visible, dataSource } = show;
  const isEdit = !!dataSource?.id;

  useEffect(() => {
    if (visible) {
      form.resetFields();
      if (dataSource) {
        form.setFieldsValue({
          ...dataSource,
          flow_id: dataSource.flow_id.toString(),
          os_type: dataSource.os_type.toString()
        });
      }
    }
  }, [visible]);

  const handleSubmit = () => {
    form.validateFields()
      .then(values => {
        const formattedValues = {
          ...values,
          flow_id: Number(selectAdFlow.value),
          os_type: Number(values.os_type)
        };
        onConfirm(isEdit ? { ...formattedValues, id: dataSource?.id } : formattedValues, isEdit);
      })
      .catch(err => message.error('表单验证失败'));
  };

  const readonlyStyle = {
    backgroundColor: '#f5f5f5',
    padding: '4px 8px',
    borderRadius: 4,
    color: '#666',
    cursor: 'not-allowed',
    display: 'inline-block',
    width: '100%' // 保持和Select相同宽度
  };

  return (
    <Modal
      title={isEdit ? '编辑媒体' : '新建媒体'}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={600}
      destroyOnClose
    >
      <Form form={form} layout="vertical" initialValues={{ enable: true }}>
        <Form.Item
          label="媒体名称"
          name="name"
          rules={[{ required: true, message: '请输入媒体名称' }]}
        >
          <Input placeholder="2-50个字符" maxLength={50} />
        </Form.Item>

        <Form.Item
          label="流量平台"
          name="flow_id"
          initialValue={selectAdFlow.value.toString()}
          rules={[{ required: true }]}
        >
          <Typography.Text code style={readonlyStyle}>
            {selectAdFlow.label}
          </Typography.Text>
        </Form.Item>

        <Form.Item
          label="系统类型"
          name="os_type"
          rules={[{ required: true, message: '请选择系统类型' }]}
        >
          <Select
            options={[
              { label: 'Android', value: 1 },
              { label: 'iOS', value: 2 }
            ]}
          />
        </Form.Item>

        <Form.Item
          label="应用包名"
          name="package_name"
          rules={[{ required: true, message: '请输入应用包名' }]}
        >
          <Input placeholder="com.example.app" />
        </Form.Item>

        <Form.Item label="下载地址" name="package_download_url">
          <Input placeholder="https://example.com/app.apk" />
        </Form.Item>

        <Form.Item label="图标地址" name="package_icon_url">
          <Input placeholder="https://example.com/icon.png" />
        </Form.Item>

        <div style={{ textAlign: 'right', marginTop: 24 }}>
          <Button onClick={onCancel} style={{ marginRight: 8 }}>取消</Button>
          <Button type="primary" onClick={handleSubmit}>确定</Button>
        </div>
      </Form>
    </Modal>
  );
};

export default ModifyMediaModal;