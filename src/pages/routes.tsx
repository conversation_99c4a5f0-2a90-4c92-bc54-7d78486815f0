import { Suspense, lazy } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';


//数据报表
//综合概况
const OverviewSummary = lazy(() => import('@/pages/data-report/overview-summary/OverviewSummary'));
// 综合报表
const ComprehensiveReport = lazy(() => import('@/pages/data-report/comprehensive-report/ComprehensiveReport'));
// 小时数据
const HourlyData = lazy(() => import('@/pages/data-report/hourly-data/HourlyData'));

// 动态导入页面组件
// budget 预算方管理
// 预算方管理
const BudgetPartyManagement  = lazy(() => import('@/pages/budget/manager/BudgetPartyManagement'));
//广告源管理
const AdSourceManagement = lazy(() => import('@/pages/budget/source/AdSourceManagement'));


//流量管理
//流量方管理
const TrafficManagement = lazy(() => import('@/pages/media-management/flow/TrafficManagement'));
//媒体管理
const MediaManagement = lazy(() => import('@/pages/media-management/media/MediaManagement'));
//广告位管理
const AdSlotManagement = lazy(() => import('@/pages/media-management/ad-slot/AdSlotManagement'))
//代码位管理
const CodeManagement = lazy(() => import('@/pages/media-management/code-slot/CodeSlotManagement'));

// 操作日志
const OperationLog = lazy(() => import('@/pages/operation-log/OperationLog'));

const AppRoutes = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <Routes>
        {/* 默认路由重定向到概览页 */}
        <Route path="/" element={<Navigate to="/overviewSummary" replace />} />
        
        {/* 数据报表模块 */}
        <Route path="/overviewSummary" element={<OverviewSummary />} />
        <Route path="/analyze/hourly" element={<HourlyData />} />
        <Route path="/analyze/comprehensive" element={<ComprehensiveReport />} />

        {/* 预算方管理模块 */}
        <Route path="/budgetManager/management" element={<BudgetPartyManagement />} />
        <Route path="/budgetManager/adSource" element={<AdSourceManagement />} />

        {/* 流量管理模块 */}
        <Route path="/flowManager/management" element={<TrafficManagement />} />
        <Route path="/flowManager/media" element={<MediaManagement />} />
        <Route path="/flowManager/adSlot" element={<AdSlotManagement />} />
        <Route path="/flowManager/adCode" element={<CodeManagement />} />

        {/* 系统日志模块 */}
        <Route path="/operationLog" element={<OperationLog />} />
      </Routes>
    </Suspense>
  );
};

export default AppRoutes;